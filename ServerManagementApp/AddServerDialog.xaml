<Window x:Class="ServerManagementApp.AddServerDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Server hinzufügen" Height="350" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0" Text="Neuen Server hinzufügen" 
                   FontSize="16" FontWeight="Bold" 
                   Margin="0,0,0,20"/>

        <Label Grid.Row="1" Content="Server-Name:" Margin="0,0,0,5"/>
        <TextBox x:Name="ServerNameTextBox" Grid.Row="1" 
                 Margin="0,25,0,10" Padding="5"/>

        <Label Grid.Row="2" Content="IP-Adresse:" Margin="0,0,0,5"/>
        <TextBox x:Name="IpAddressTextBox" Grid.Row="2" 
                 Margin="0,25,0,10" Padding="5"/>

        <Label Grid.Row="3" Content="Beschreibung:" Margin="0,0,0,5"/>
        <TextBox x:Name="DescriptionTextBox" Grid.Row="3" 
                 Margin="0,25,0,10" Padding="5"/>

        <Label Grid.Row="4" Content="RAM (GB):" Margin="0,0,0,5"/>
        <TextBox x:Name="MemoryTextBox" Grid.Row="4" 
                 Margin="0,25,0,10" Padding="5" Text="8"/>

        <StackPanel Grid.Row="7" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="Abbrechen" Width="80" Height="30" 
                    Margin="0,0,10,0" Click="CancelButton_Click"/>
            <Button Content="Hinzufügen" Width="80" Height="30" 
                    Background="#FF0078D4" Foreground="White"
                    Click="AddButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
