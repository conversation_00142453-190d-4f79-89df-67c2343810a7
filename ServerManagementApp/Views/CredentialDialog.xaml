<Window x:Class="ServerManagementApp.Services.CredentialDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Server-Anmeldedaten"
        Height="320" Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#020817">

    <Border Background="#0F172A" CornerRadius="8" Margin="20" BorderBrush="#1E293B" BorderThickness="1">
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                <Border Background="#1E293B" CornerRadius="8" Width="48" Height="48" Margin="0,0,16,0">
                    <TextBlock Text="🔐" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <StackPanel VerticalAlignment="Center">
                    <TextBlock Text="Server-Anmeldedaten" FontSize="18" FontWeight="SemiBold" Foreground="#F8FAFC" Margin="0,0,0,4"/>
                    <TextBlock Text="Geben Sie Ihre Anmeldedaten für Remote-Server ein" FontSize="12" Foreground="#94A3B8"/>
                </StackPanel>
            </StackPanel>

            <!-- Username Label -->
            <TextBlock Grid.Row="1" Text="Benutzername:" FontSize="14" FontWeight="Medium" Foreground="#F8FAFC" Margin="0,0,0,8"/>

            <!-- Username Input -->
            <Border Grid.Row="2" Background="#1E293B" CornerRadius="6" BorderBrush="#374151" BorderThickness="1" Margin="0,0,0,16">
                <TextBox x:Name="UsernameTextBox" 
                         Background="Transparent" 
                         BorderThickness="0" 
                         Padding="12,10"
                         FontSize="14"
                         Foreground="#F8FAFC"
                         Text="DOMAIN\username"/>
            </Border>

            <!-- Password Label -->
            <TextBlock Grid.Row="3" Text="Passwort:" FontSize="14" FontWeight="Medium" Foreground="#F8FAFC" Margin="0,0,0,8"/>

            <!-- Password Input -->
            <Border Grid.Row="4" Background="#1E293B" CornerRadius="6" BorderBrush="#374151" BorderThickness="1" Margin="0,0,0,16">
                <PasswordBox x:Name="PasswordBox" 
                             Background="Transparent" 
                             BorderThickness="0" 
                             Padding="12,10"
                             FontSize="14"
                             Foreground="#F8FAFC"/>
            </Border>

            <!-- Info Text -->
            <Border Grid.Row="5" Background="#1E293B" CornerRadius="6" Padding="12,8" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="ℹ️ Hinweis" FontSize="12" FontWeight="SemiBold" Foreground="#3B82F6" Margin="0,0,0,4"/>
                    <TextBlock Text="Diese Anmeldedaten werden für Remote-PowerShell-Verbindungen zu den Servern verwendet." 
                               FontSize="11" 
                               Foreground="#94A3B8" 
                               TextWrapping="Wrap"/>
                </StackPanel>
            </Border>

            <!-- Buttons -->
            <Grid Grid.Row="7">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <Button Grid.Column="1" 
                        x:Name="CancelButton"
                        Content="Abbrechen" 
                        Background="Transparent"
                        BorderBrush="#374151"
                        BorderThickness="1"
                        Foreground="#94A3B8"
                        Padding="16,8"
                        Margin="0,0,12,0"
                        Click="CancelButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#374151"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>

                <Button Grid.Column="2" 
                        x:Name="OkButton"
                        Content="OK" 
                        Background="#3B82F6"
                        BorderThickness="0"
                        Foreground="White"
                        Padding="20,8"
                        Click="OkButton_Click"
                        IsDefault="True">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#2563EB"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Grid>
    </Border>
</Window>
