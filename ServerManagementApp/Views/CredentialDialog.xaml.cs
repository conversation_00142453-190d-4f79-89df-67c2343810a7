using System;
using System.Runtime.InteropServices;
using System.Security;
using System.Windows;

namespace ServerManagementApp.Services
{
    public partial class CredentialDialog : Window
    {
        public string Username { get; private set; }
        public SecureString Password { get; private set; }

        public CredentialDialog()
        {
            InitializeComponent();
            
            // Focus auf Username-Feld setzen
            Loaded += (s, e) => UsernameTextBox.Focus();
            
            // Enter-Taste im Password-Feld soll OK auslösen
            PasswordBox.KeyDown += (s, e) =>
            {
                if (e.Key == System.Windows.Input.Key.Enter)
                {
                    OkButton_Click(s, e);
                }
            };

            // Placeholder-Text entfernen wenn User klickt
            UsernameTextBox.GotFocus += (s, e) =>
            {
                if (UsernameTextBox.Text == "DOMAIN\\username")
                {
                    UsernameTextBox.Text = "";
                    UsernameTextBox.Foreground = System.Windows.Media.Brushes.White;
                }
            };

            // Placeholder-Text wieder hinzufügen wenn leer
            UsernameTextBox.LostFocus += (s, e) =>
            {
                if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
                {
                    UsernameTextBox.Text = "DOMAIN\\username";
                    UsernameTextBox.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(148, 163, 184));
                }
            };
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            // Validierung
            if (string.IsNullOrWhiteSpace(UsernameTextBox.Text) || UsernameTextBox.Text == "DOMAIN\\username")
            {
                MessageBox.Show("Bitte geben Sie einen gültigen Benutzernamen ein.", 
                    "Eingabe erforderlich", MessageBoxButton.OK, MessageBoxImage.Warning);
                UsernameTextBox.Focus();
                return;
            }

            if (PasswordBox.Password.Length == 0)
            {
                MessageBox.Show("Bitte geben Sie ein Passwort ein.", 
                    "Eingabe erforderlich", MessageBoxButton.OK, MessageBoxImage.Warning);
                PasswordBox.Focus();
                return;
            }

            // Daten speichern
            Username = UsernameTextBox.Text.Trim();
            Password = ConvertToSecureString(PasswordBox.Password);

            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private SecureString ConvertToSecureString(string password)
        {
            var secureString = new SecureString();
            foreach (char c in password)
            {
                secureString.AppendChar(c);
            }
            secureString.MakeReadOnly();
            return secureString;
        }

        protected override void OnClosed(EventArgs e)
        {
            // Passwort aus Memory löschen
            PasswordBox.Clear();
            base.OnClosed(e);
        }
    }
}
