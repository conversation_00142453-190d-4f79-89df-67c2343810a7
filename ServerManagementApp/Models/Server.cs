using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;

namespace ServerManagementApp.Models
{
    public class Server : INotifyPropertyChanged
    {
        private string _name;
        private string _ipAddress;
        private string _description;
        private ServerStatus _status;
        private int _cpuUsage;
        private long _memoryUsed;
        private long _memoryTotal;
        private DateTime _lastPing;
        private int _pingTime;
        private DateTime? _lastUpdate;
        private ObservableCollection<DiskPartition> _diskPartitions;

        public Server()
        {
            _diskPartitions = new ObservableCollection<DiskPartition>();
        }

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
            }
        }

        public string IpAddress
        {
            get => _ipAddress;
            set
            {
                _ipAddress = value;
                OnPropertyChanged();
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                _description = value;
                OnPropertyChanged();
            }
        }

        public ServerStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(StatusText));
                OnPropertyChanged(nameof(StatusColor));
            }
        }

        public int CpuUsage
        {
            get => _cpuUsage;
            set
            {
                _cpuUsage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CpuUsageText));
            }
        }

        public long MemoryUsed
        {
            get => _memoryUsed;
            set
            {
                _memoryUsed = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(MemoryUsageText));
            }
        }

        public long MemoryTotal
        {
            get => _memoryTotal;
            set
            {
                _memoryTotal = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(MemoryUsageText));
            }
        }

        public DateTime LastPing
        {
            get => _lastPing;
            set
            {
                _lastPing = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(LastPingText));
            }
        }

        public int PingTime
        {
            get => _pingTime;
            set
            {
                _pingTime = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PingTimeText));
            }
        }

        public DateTime? LastUpdate
        {
            get => _lastUpdate;
            set
            {
                _lastUpdate = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(LastUpdateText));
            }
        }

        // Computed Properties
        public string StatusText => Status switch
        {
            ServerStatus.Online => "Online",
            ServerStatus.Offline => "Offline",
            ServerStatus.Warning => "Warnung",
            ServerStatus.Maintenance => "Wartung",
            _ => "Unbekannt"
        };

        public string StatusColor => Status switch
        {
            ServerStatus.Online => "Green",
            ServerStatus.Offline => "Red",
            ServerStatus.Warning => "Orange",
            ServerStatus.Maintenance => "Blue",
            _ => "Gray"
        };

        public string CpuUsageText => $"CPU: {CpuUsage}%";

        public string MemoryUsageText => MemoryTotal > 0 
            ? $"RAM: {MemoryUsed / (1024 * 1024 * 1024):F1}GB / {MemoryTotal / (1024 * 1024 * 1024):F1}GB"
            : "RAM: N/A";

        public string LastPingText => LastPing != DateTime.MinValue
            ? $"Letzter Ping: {LastPing:HH:mm:ss}"
            : "Noch nicht gepingt";

        // Neue Eigenschaften für erweiterte Informationen
        public DateTime LastBootTime { get; set; } = DateTime.Now.AddDays(-new Random().Next(1, 30));
        public string OperatingSystem { get; set; } = "Windows Server 2022";
        public bool HasPendingUpdates { get; set; }
        public bool RequiresReboot { get; set; }
        public List<string> Alerts { get; set; } = new List<string>();

        // Computed Properties für UI
        public string UptimeText
        {
            get
            {
                var uptime = DateTime.Now - LastBootTime;
                if (uptime.TotalDays >= 1)
                    return $"{uptime.Days}d {uptime.Hours}h";
                else if (uptime.TotalHours >= 1)
                    return $"{uptime.Hours}h {uptime.Minutes}m";
                else
                    return $"{uptime.Minutes}m";
            }
        }

        public string OsVersion
        {
            get
            {
                return OperatingSystem switch
                {
                    var os when os.Contains("2022") => "2022",
                    var os when os.Contains("2019") => "2019",
                    var os when os.Contains("2016") => "2016",
                    var os when os.Contains("Linux") => "Linux",
                    _ => "Win"
                };
            }
        }

        public string OsIcon
        {
            get
            {
                return OperatingSystem switch
                {
                    var os when os.Contains("Windows") => "🪟",
                    var os when os.Contains("Linux") => "🐧",
                    _ => "💻"
                };
            }
        }

        public string AlertText
        {
            get
            {
                if (Alerts.Any())
                    return Alerts.First();
                if (RequiresReboot)
                    return "Neustart erforderlich";
                if (HasPendingUpdates)
                    return "Updates verfügbar";
                if (DiskPartitions.Any(d => d.UsagePercent > 90))
                    return "Festplatte voll";
                return "OK";
            }
        }

        public string AlertIcon
        {
            get
            {
                if (Alerts.Any() || RequiresReboot)
                    return "⚠️";
                if (HasPendingUpdates)
                    return "📦";
                if (DiskPartitions.Any(d => d.UsagePercent > 90))
                    return "💾";
                return "✅";
            }
        }

        public string AlertBackgroundColor
        {
            get
            {
                if (Alerts.Any() || RequiresReboot)
                    return "#FFEBEE";
                if (HasPendingUpdates)
                    return "#FFF3E0";
                if (DiskPartitions.Any(d => d.UsagePercent > 90))
                    return "#FFEBEE";
                return "#E8F5E8";
            }
        }

        public string AlertTextColor
        {
            get
            {
                if (Alerts.Any() || RequiresReboot)
                    return "#D32F2F";
                if (HasPendingUpdates)
                    return "#F57C00";
                if (DiskPartitions.Any(d => d.UsagePercent > 90))
                    return "#D32F2F";
                return "#2E7D32";
            }
        }

        public string PingTimeText => PingTime > 0
            ? $"Ping: {PingTime}ms"
            : "";

        public string LastUpdateText => LastUpdate.HasValue
            ? $"Zuletzt aktualisiert: {LastUpdate.Value:HH:mm:ss}"
            : "Noch nicht aktualisiert";

        public ObservableCollection<DiskPartition> DiskPartitions
        {
            get => _diskPartitions;
            set
            {
                _diskPartitions = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(DiskSummaryText));
            }
        }

        public string DiskSummaryText
        {
            get
            {
                if (DiskPartitions == null || DiskPartitions.Count == 0)
                    return "Festplatten: N/A";

                var totalUsedGB = 0.0;
                var totalSizeGB = 0.0;

                foreach (var partition in DiskPartitions)
                {
                    totalUsedGB += partition.UsedSpaceGB;
                    totalSizeGB += partition.TotalSpaceGB;
                }

                var usagePercent = totalSizeGB > 0 ? (totalUsedGB / totalSizeGB) * 100 : 0;
                return $"Festplatten: {usagePercent:F1}% ({totalUsedGB:F1}/{totalSizeGB:F1} GB)";
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public enum ServerStatus
    {
        Unknown,
        Online,
        Offline,
        Warning,
        Maintenance
    }

    public class DiskPartition : INotifyPropertyChanged
    {
        private string _driveLetter;
        private string _label;
        private double _totalSpaceGB;
        private double _usedSpaceGB;

        public string DriveLetter
        {
            get => _driveLetter;
            set
            {
                _driveLetter = value;
                OnPropertyChanged();
            }
        }

        public string Label
        {
            get => _label;
            set
            {
                _label = value;
                OnPropertyChanged();
            }
        }

        public double TotalSpaceGB
        {
            get => _totalSpaceGB;
            set
            {
                _totalSpaceGB = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(UsagePercent));
                OnPropertyChanged(nameof(DisplayText));
            }
        }

        public double UsedSpaceGB
        {
            get => _usedSpaceGB;
            set
            {
                _usedSpaceGB = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(UsagePercent));
                OnPropertyChanged(nameof(DisplayText));
            }
        }

        public double UsagePercent => TotalSpaceGB > 0 ? (UsedSpaceGB / TotalSpaceGB) * 100 : 0;

        public string DisplayText => $"{DriveLetter}: {UsagePercent:F1}% ({UsedSpaceGB:F1}/{TotalSpaceGB:F1} GB)";

        public string UsageColor => UsagePercent switch
        {
            >= 90 => "Red",
            >= 80 => "Orange",
            >= 70 => "Gold",
            _ => "Green"
        };

        public string UsageTextColor => UsagePercent switch
        {
            >= 90 => "#EF4444",
            >= 80 => "#F59E0B",
            >= 70 => "#D97706",
            _ => "#10B981"
        };

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
