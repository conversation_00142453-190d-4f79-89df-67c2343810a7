using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ServerManagementApp.Models
{
    public class Server : INotifyPropertyChanged
    {
        private string _name;
        private string _ipAddress;
        private string _description;
        private ServerStatus _status;
        private int _cpuUsage;
        private long _memoryUsed;
        private long _memoryTotal;
        private DateTime _lastPing;
        private int _pingTime;

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
            }
        }

        public string IpAddress
        {
            get => _ipAddress;
            set
            {
                _ipAddress = value;
                OnPropertyChanged();
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                _description = value;
                OnPropertyChanged();
            }
        }

        public ServerStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(StatusText));
                OnPropertyChanged(nameof(StatusColor));
            }
        }

        public int CpuUsage
        {
            get => _cpuUsage;
            set
            {
                _cpuUsage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CpuUsageText));
            }
        }

        public long MemoryUsed
        {
            get => _memoryUsed;
            set
            {
                _memoryUsed = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(MemoryUsageText));
            }
        }

        public long MemoryTotal
        {
            get => _memoryTotal;
            set
            {
                _memoryTotal = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(MemoryUsageText));
            }
        }

        public DateTime LastPing
        {
            get => _lastPing;
            set
            {
                _lastPing = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(LastPingText));
            }
        }

        public int PingTime
        {
            get => _pingTime;
            set
            {
                _pingTime = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PingTimeText));
            }
        }

        // Computed Properties
        public string StatusText => Status switch
        {
            ServerStatus.Online => "Online",
            ServerStatus.Offline => "Offline",
            ServerStatus.Warning => "Warnung",
            ServerStatus.Maintenance => "Wartung",
            _ => "Unbekannt"
        };

        public string StatusColor => Status switch
        {
            ServerStatus.Online => "Green",
            ServerStatus.Offline => "Red",
            ServerStatus.Warning => "Orange",
            ServerStatus.Maintenance => "Blue",
            _ => "Gray"
        };

        public string CpuUsageText => $"CPU: {CpuUsage}%";

        public string MemoryUsageText => MemoryTotal > 0 
            ? $"RAM: {MemoryUsed / (1024 * 1024 * 1024):F1}GB / {MemoryTotal / (1024 * 1024 * 1024):F1}GB"
            : "RAM: N/A";

        public string LastPingText => LastPing != DateTime.MinValue 
            ? $"Letzter Ping: {LastPing:HH:mm:ss}"
            : "Noch nicht gepingt";

        public string PingTimeText => PingTime > 0 
            ? $"Ping: {PingTime}ms"
            : "";

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public enum ServerStatus
    {
        Unknown,
        Online,
        Offline,
        Warning,
        Maintenance
    }
}
