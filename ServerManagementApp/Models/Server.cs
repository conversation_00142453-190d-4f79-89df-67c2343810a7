using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ServerManagementApp.Models
{
    public class Server : INotifyPropertyChanged
    {
        private string _name;
        private string _ipAddress;
        private string _description;
        private ServerStatus _status;
        private int _cpuUsage;
        private long _memoryUsed;
        private long _memoryTotal;
        private DateTime _lastPing;
        private int _pingTime;
        private ObservableCollection<DiskPartition> _diskPartitions;

        public Server()
        {
            _diskPartitions = new ObservableCollection<DiskPartition>();
        }

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
            }
        }

        public string IpAddress
        {
            get => _ipAddress;
            set
            {
                _ipAddress = value;
                OnPropertyChanged();
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                _description = value;
                OnPropertyChanged();
            }
        }

        public ServerStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(StatusText));
                OnPropertyChanged(nameof(StatusColor));
            }
        }

        public int CpuUsage
        {
            get => _cpuUsage;
            set
            {
                _cpuUsage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CpuUsageText));
            }
        }

        public long MemoryUsed
        {
            get => _memoryUsed;
            set
            {
                _memoryUsed = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(MemoryUsageText));
            }
        }

        public long MemoryTotal
        {
            get => _memoryTotal;
            set
            {
                _memoryTotal = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(MemoryUsageText));
            }
        }

        public DateTime LastPing
        {
            get => _lastPing;
            set
            {
                _lastPing = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(LastPingText));
            }
        }

        public int PingTime
        {
            get => _pingTime;
            set
            {
                _pingTime = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PingTimeText));
            }
        }

        // Computed Properties
        public string StatusText => Status switch
        {
            ServerStatus.Online => "Online",
            ServerStatus.Offline => "Offline",
            ServerStatus.Warning => "Warnung",
            ServerStatus.Maintenance => "Wartung",
            _ => "Unbekannt"
        };

        public string StatusColor => Status switch
        {
            ServerStatus.Online => "Green",
            ServerStatus.Offline => "Red",
            ServerStatus.Warning => "Orange",
            ServerStatus.Maintenance => "Blue",
            _ => "Gray"
        };

        public string CpuUsageText => $"CPU: {CpuUsage}%";

        public string MemoryUsageText => MemoryTotal > 0 
            ? $"RAM: {MemoryUsed / (1024 * 1024 * 1024):F1}GB / {MemoryTotal / (1024 * 1024 * 1024):F1}GB"
            : "RAM: N/A";

        public string LastPingText => LastPing != DateTime.MinValue
            ? $"Letzter Ping: {LastPing:HH:mm:ss}"
            : "Noch nicht gepingt";

        public string PingTimeText => PingTime > 0
            ? $"Ping: {PingTime}ms"
            : "";

        public ObservableCollection<DiskPartition> DiskPartitions
        {
            get => _diskPartitions;
            set
            {
                _diskPartitions = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(DiskSummaryText));
            }
        }

        public string DiskSummaryText
        {
            get
            {
                if (DiskPartitions == null || DiskPartitions.Count == 0)
                    return "Festplatten: N/A";

                var totalUsedGB = 0.0;
                var totalSizeGB = 0.0;

                foreach (var partition in DiskPartitions)
                {
                    totalUsedGB += partition.UsedSpaceGB;
                    totalSizeGB += partition.TotalSpaceGB;
                }

                var usagePercent = totalSizeGB > 0 ? (totalUsedGB / totalSizeGB) * 100 : 0;
                return $"Festplatten: {usagePercent:F1}% ({totalUsedGB:F1}/{totalSizeGB:F1} GB)";
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public enum ServerStatus
    {
        Unknown,
        Online,
        Offline,
        Warning,
        Maintenance
    }

    public class DiskPartition : INotifyPropertyChanged
    {
        private string _driveLetter;
        private string _label;
        private double _totalSpaceGB;
        private double _usedSpaceGB;

        public string DriveLetter
        {
            get => _driveLetter;
            set
            {
                _driveLetter = value;
                OnPropertyChanged();
            }
        }

        public string Label
        {
            get => _label;
            set
            {
                _label = value;
                OnPropertyChanged();
            }
        }

        public double TotalSpaceGB
        {
            get => _totalSpaceGB;
            set
            {
                _totalSpaceGB = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(UsagePercent));
                OnPropertyChanged(nameof(DisplayText));
            }
        }

        public double UsedSpaceGB
        {
            get => _usedSpaceGB;
            set
            {
                _usedSpaceGB = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(UsagePercent));
                OnPropertyChanged(nameof(DisplayText));
            }
        }

        public double UsagePercent => TotalSpaceGB > 0 ? (UsedSpaceGB / TotalSpaceGB) * 100 : 0;

        public string DisplayText => $"{DriveLetter}: {UsagePercent:F1}% ({UsedSpaceGB:F1}/{TotalSpaceGB:F1} GB)";

        public string UsageColor => UsagePercent switch
        {
            >= 90 => "Red",
            >= 80 => "Orange",
            >= 70 => "Gold",
            _ => "Green"
        };

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
