using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text.Json;
using System.Threading.Tasks;
using ServerManagementApp.Models;

namespace ServerManagementApp.Services
{
    public class ServerService
    {
        private const string ServersFileName = "servers.json";
        private readonly ObservableCollection<Server> _servers;

        public ServerService()
        {
            _servers = new ObservableCollection<Server>();
            LoadServers();
        }

        public ObservableCollection<Server> Servers => _servers;

        public void InitializeDefaultServers()
        {
            if (_servers.Count == 0)
            {
                var defaultServers = new List<Server>
                {
                    new Server
                    {
                        Name = "DC01",
                        IpAddress = "*************",
                        Description = "Domain Controller 1",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 8L * 1024 * 1024 * 1024 // 8GB
                    },
                    new Server
                    {
                        Name = "DC02",
                        IpAddress = "*************",
                        Description = "Domain Controller 2",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 8L * 1024 * 1024 * 1024 // 8GB
                    },
                    new Server
                    {
                        Name = "APP01",
                        IpAddress = "*************",
                        Description = "Application Server",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 16L * 1024 * 1024 * 1024 // 16GB
                    },
                    new Server
                    {
                        Name = "DB01",
                        IpAddress = "************",
                        Description = "Database Server",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 32L * 1024 * 1024 * 1024 // 32GB
                    },
                    new Server
                    {
                        Name = "MAIL01",
                        IpAddress = "************",
                        Description = "Mail Server",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 16L * 1024 * 1024 * 1024 // 16GB
                    },
                    new Server
                    {
                        Name = "FILE",
                        IpAddress = "*************",
                        Description = "File Server",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 12L * 1024 * 1024 * 1024 // 12GB
                    },
                    new Server
                    {
                        Name = "TS01",
                        IpAddress = "*************",
                        Description = "Terminal Server",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 24L * 1024 * 1024 * 1024 // 24GB
                    }
                };

                foreach (var server in defaultServers)
                {
                    _servers.Add(server);
                }

                SaveServers();
            }
        }

        public async Task<bool> PingServerAsync(Server server)
        {
            try
            {
                using var ping = new Ping();
                var reply = await ping.SendPingAsync(server.IpAddress, 5000);
                
                server.LastPing = DateTime.Now;
                
                if (reply.Status == IPStatus.Success)
                {
                    server.Status = ServerStatus.Online;
                    server.PingTime = (int)reply.RoundtripTime;
                    
                    // Simuliere CPU und Memory Werte (in echter Anwendung würde man WMI/SNMP verwenden)
                    var random = new Random();
                    server.CpuUsage = random.Next(10, 80);
                    server.MemoryUsed = (long)(server.MemoryTotal * (random.NextDouble() * 0.6 + 0.2)); // 20-80% Auslastung
                    
                    return true;
                }
                else
                {
                    server.Status = ServerStatus.Offline;
                    server.PingTime = 0;
                    return false;
                }
            }
            catch (Exception)
            {
                server.Status = ServerStatus.Offline;
                server.PingTime = 0;
                return false;
            }
        }

        public async Task PingAllServersAsync()
        {
            var tasks = _servers.Select(PingServerAsync).ToArray();
            await Task.WhenAll(tasks);
        }

        public void AddServer(Server server)
        {
            _servers.Add(server);
            SaveServers();
        }

        public void RemoveServer(Server server)
        {
            _servers.Remove(server);
            SaveServers();
        }

        public void SaveServers()
        {
            try
            {
                var serversData = _servers.Select(s => new
                {
                    s.Name,
                    s.IpAddress,
                    s.Description,
                    s.MemoryTotal
                }).ToList();

                var json = JsonSerializer.Serialize(serversData, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                File.WriteAllText(ServersFileName, json);
            }
            catch (Exception ex)
            {
                // In einer echten Anwendung würde man hier Logging verwenden
                System.Diagnostics.Debug.WriteLine($"Fehler beim Speichern der Server: {ex.Message}");
            }
        }

        private void LoadServers()
        {
            try
            {
                if (File.Exists(ServersFileName))
                {
                    var json = File.ReadAllText(ServersFileName);
                    var serversData = JsonSerializer.Deserialize<List<dynamic>>(json);
                    
                    // Hier würde man normalerweise die JSON-Daten richtig deserialisieren
                    // Für jetzt initialisieren wir die Standard-Server
                }
            }
            catch (Exception ex)
            {
                // In einer echten Anwendung würde man hier Logging verwenden
                System.Diagnostics.Debug.WriteLine($"Fehler beim Laden der Server: {ex.Message}");
            }
        }
    }
}
