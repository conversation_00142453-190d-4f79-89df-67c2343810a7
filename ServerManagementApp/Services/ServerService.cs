using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text.Json;
using System.Threading.Tasks;
using ServerManagementApp.Models;

namespace ServerManagementApp.Services
{
    public class ServerService
    {
        private const string ServersFileName = "servers.json";
        private readonly ObservableCollection<Server> _servers;
        private readonly PowerShellService _powerShellService;

        public ServerService()
        {
            _servers = new ObservableCollection<Server>();
            _powerShellService = new PowerShellService();
            LoadServers();
        }

        public ObservableCollection<Server> Servers => _servers;

        public void InitializeDefaultServers()
        {
            if (_servers.Count == 0)
            {
                var defaultServers = new List<Server>
                {
                    new Server
                    {
                        Name = "LOCALHOST",
                        IpAddress = "127.0.0.1",
                        Description = "Local Development Machine",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 64L * 1024 * 1024 * 1024 // 64GB
                    },
                    new Server
                    {
                        Name = "DC01",
                        IpAddress = "*************",
                        Description = "Domain Controller 1",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 8L * 1024 * 1024 * 1024 // 8GB
                    },
                    new Server
                    {
                        Name = "DC02",
                        IpAddress = "*************",
                        Description = "Domain Controller 2",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 8L * 1024 * 1024 * 1024 // 8GB
                    },
                    new Server
                    {
                        Name = "APP01",
                        IpAddress = "*************",
                        Description = "Application Server",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 16L * 1024 * 1024 * 1024 // 16GB
                    },
                    new Server
                    {
                        Name = "DB01",
                        IpAddress = "************",
                        Description = "Database Server",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 32L * 1024 * 1024 * 1024 // 32GB
                    },
                    new Server
                    {
                        Name = "MAIL01",
                        IpAddress = "************",
                        Description = "Mail Server",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 16L * 1024 * 1024 * 1024 // 16GB
                    },
                    new Server
                    {
                        Name = "FILE",
                        IpAddress = "*************",
                        Description = "File Server",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 12L * 1024 * 1024 * 1024 // 12GB
                    },
                    new Server
                    {
                        Name = "TS01",
                        IpAddress = "*************",
                        Description = "Terminal Server",
                        Status = ServerStatus.Unknown,
                        MemoryTotal = 24L * 1024 * 1024 * 1024 // 24GB
                    }
                };

                foreach (var server in defaultServers)
                {
                    _servers.Add(server);
                }

                SaveServers();
            }
        }

        public async Task<bool> PingServerAsync(Server server)
        {
            try
            {
                using var ping = new Ping();
                var reply = await ping.SendPingAsync(server.IpAddress, 5000);

                server.LastPing = DateTime.Now;

                if (reply.Status == IPStatus.Success)
                {
                    server.Status = ServerStatus.Online;
                    server.PingTime = (int)reply.RoundtripTime;

                    // Echte Systemdaten abrufen
                    await UpdateServerSystemInfoAsync(server);

                    return true;
                }
                else
                {
                    server.Status = ServerStatus.Offline;
                    server.PingTime = 0;
                    return false;
                }
            }
            catch (Exception)
            {
                server.Status = ServerStatus.Offline;
                server.PingTime = 0;
                return false;
            }
        }

        private async Task UpdateServerSystemInfoAsync(Server server)
        {
            try
            {
                // Für localhost verwenden wir echte Daten, für Remote-Server simulieren wir erstmal
                var computerName = server.IpAddress == "127.0.0.1" || server.Name.ToLower() == "localhost"
                    ? "localhost"
                    : server.IpAddress;

                SystemInfo systemInfo;

                if (computerName == "localhost")
                {
                    // Echte Daten für localhost
                    systemInfo = await _powerShellService.GetSystemInfoAsync("localhost");
                }
                else
                {
                    // Für Remote-Server erstmal simulierte Daten (später Remote PowerShell)
                    systemInfo = GenerateSimulatedSystemInfo(server);
                }

                // Server-Daten aktualisieren
                server.CpuUsage = systemInfo.CpuUsage;
                server.MemoryTotal = (long)(systemInfo.TotalMemoryGB * 1024 * 1024 * 1024);
                server.MemoryUsed = (long)(systemInfo.UsedMemoryGB * 1024 * 1024 * 1024);

                // Festplatten-Daten aktualisieren
                server.DiskPartitions.Clear();
                foreach (var partition in systemInfo.DiskPartitions)
                {
                    server.DiskPartitions.Add(partition);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Fehler beim Aktualisieren der Systemdaten für {server.Name}: {ex.Message}");

                // Fallback zu simulierten Daten
                var systemInfo = GenerateSimulatedSystemInfo(server);
                server.CpuUsage = systemInfo.CpuUsage;
                server.MemoryUsed = (long)(server.MemoryTotal * (new Random().NextDouble() * 0.6 + 0.2));
            }
        }

        private SystemInfo GenerateSimulatedSystemInfo(Server server)
        {
            var random = new Random();
            var systemInfo = new SystemInfo
            {
                CpuUsage = random.Next(10, 80),
                TotalMemoryGB = server.MemoryTotal / (1024.0 * 1024 * 1024),
                UsedMemoryGB = (server.MemoryTotal / (1024.0 * 1024 * 1024)) * (random.NextDouble() * 0.6 + 0.2)
            };

            // Simulierte Festplatten basierend auf Server-Typ
            switch (server.Name)
            {
                case "DC01":
                case "DC02":
                    systemInfo.DiskPartitions.Add(new DiskPartition { DriveLetter = "C", Label = "System", TotalSpaceGB = 100, UsedSpaceGB = 45 + random.NextDouble() * 20 });
                    systemInfo.DiskPartitions.Add(new DiskPartition { DriveLetter = "D", Label = "Logs", TotalSpaceGB = 50, UsedSpaceGB = 15 + random.NextDouble() * 15 });
                    break;
                case "DB01":
                    systemInfo.DiskPartitions.Add(new DiskPartition { DriveLetter = "C", Label = "System", TotalSpaceGB = 100, UsedSpaceGB = 35 + random.NextDouble() * 15 });
                    systemInfo.DiskPartitions.Add(new DiskPartition { DriveLetter = "D", Label = "Data", TotalSpaceGB = 500, UsedSpaceGB = 280 + random.NextDouble() * 100 });
                    systemInfo.DiskPartitions.Add(new DiskPartition { DriveLetter = "E", Label = "Logs", TotalSpaceGB = 100, UsedSpaceGB = 45 + random.NextDouble() * 25 });
                    break;
                case "FILE":
                    systemInfo.DiskPartitions.Add(new DiskPartition { DriveLetter = "C", Label = "System", TotalSpaceGB = 100, UsedSpaceGB = 40 + random.NextDouble() * 20 });
                    systemInfo.DiskPartitions.Add(new DiskPartition { DriveLetter = "D", Label = "Data", TotalSpaceGB = 2000, UsedSpaceGB = 1200 + random.NextDouble() * 400 });
                    systemInfo.DiskPartitions.Add(new DiskPartition { DriveLetter = "E", Label = "Backup", TotalSpaceGB = 1000, UsedSpaceGB = 650 + random.NextDouble() * 200 });
                    break;
                default:
                    systemInfo.DiskPartitions.Add(new DiskPartition { DriveLetter = "C", Label = "System", TotalSpaceGB = 100, UsedSpaceGB = 40 + random.NextDouble() * 25 });
                    systemInfo.DiskPartitions.Add(new DiskPartition { DriveLetter = "D", Label = "Data", TotalSpaceGB = 200, UsedSpaceGB = 80 + random.NextDouble() * 60 });
                    break;
            }

            return systemInfo;
        }

        public async Task PingAllServersAsync()
        {
            var tasks = _servers.Select(PingServerAsync).ToArray();
            await Task.WhenAll(tasks);
        }

        public void AddServer(Server server)
        {
            _servers.Add(server);
            SaveServers();
        }

        public void RemoveServer(Server server)
        {
            _servers.Remove(server);
            SaveServers();
        }

        public void SaveServers()
        {
            try
            {
                var serversData = _servers.Select(s => new
                {
                    s.Name,
                    s.IpAddress,
                    s.Description,
                    s.MemoryTotal
                }).ToList();

                var json = JsonSerializer.Serialize(serversData, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                File.WriteAllText(ServersFileName, json);
            }
            catch (Exception ex)
            {
                // In einer echten Anwendung würde man hier Logging verwenden
                System.Diagnostics.Debug.WriteLine($"Fehler beim Speichern der Server: {ex.Message}");
            }
        }

        private void LoadServers()
        {
            try
            {
                if (File.Exists(ServersFileName))
                {
                    var json = File.ReadAllText(ServersFileName);
                    var serversData = JsonSerializer.Deserialize<List<dynamic>>(json);

                    // Hier würde man normalerweise die JSON-Daten richtig deserialisieren
                    // Für jetzt initialisieren wir die Standard-Server
                }
            }
            catch (Exception ex)
            {
                // In einer echten Anwendung würde man hier Logging verwenden
                System.Diagnostics.Debug.WriteLine($"Fehler beim Laden der Server: {ex.Message}");
            }
        }

        private void GenerateSimulatedDiskData(Server server, Random random)
        {
            // Simuliere typische Server-Festplatten basierend auf Server-Typ
            switch (server.Name)
            {
                case "DC01":
                case "DC02":
                    // Domain Controller - typisch C: System, D: Logs
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "C",
                        Label = "System",
                        TotalSpaceGB = 100,
                        UsedSpaceGB = 45 + random.NextDouble() * 20
                    });
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "D",
                        Label = "Logs",
                        TotalSpaceGB = 50,
                        UsedSpaceGB = 15 + random.NextDouble() * 15
                    });
                    break;

                case "DB01":
                    // Database Server - C: System, D: Data, E: Logs, F: TempDB
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "C",
                        Label = "System",
                        TotalSpaceGB = 100,
                        UsedSpaceGB = 35 + random.NextDouble() * 15
                    });
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "D",
                        Label = "Data",
                        TotalSpaceGB = 500,
                        UsedSpaceGB = 280 + random.NextDouble() * 100
                    });
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "E",
                        Label = "Logs",
                        TotalSpaceGB = 100,
                        UsedSpaceGB = 45 + random.NextDouble() * 25
                    });
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "F",
                        Label = "TempDB",
                        TotalSpaceGB = 50,
                        UsedSpaceGB = 8 + random.NextDouble() * 12
                    });
                    break;

                case "FILE":
                    // File Server - C: System, D: Data, E: Backup
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "C",
                        Label = "System",
                        TotalSpaceGB = 100,
                        UsedSpaceGB = 40 + random.NextDouble() * 20
                    });
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "D",
                        Label = "Data",
                        TotalSpaceGB = 2000,
                        UsedSpaceGB = 1200 + random.NextDouble() * 400
                    });
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "E",
                        Label = "Backup",
                        TotalSpaceGB = 1000,
                        UsedSpaceGB = 650 + random.NextDouble() * 200
                    });
                    break;

                case "MAIL01":
                    // Mail Server - C: System, D: Mailboxes, E: Logs
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "C",
                        Label = "System",
                        TotalSpaceGB = 100,
                        UsedSpaceGB = 45 + random.NextDouble() * 15
                    });
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "D",
                        Label = "Mailboxes",
                        TotalSpaceGB = 800,
                        UsedSpaceGB = 520 + random.NextDouble() * 150
                    });
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "E",
                        Label = "Logs",
                        TotalSpaceGB = 100,
                        UsedSpaceGB = 35 + random.NextDouble() * 25
                    });
                    break;

                default:
                    // Standard Server - C: System, D: Data
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "C",
                        Label = "System",
                        TotalSpaceGB = 100,
                        UsedSpaceGB = 40 + random.NextDouble() * 25
                    });
                    server.DiskPartitions.Add(new DiskPartition
                    {
                        DriveLetter = "D",
                        Label = "Data",
                        TotalSpaceGB = 200,
                        UsedSpaceGB = 80 + random.NextDouble() * 60
                    });
                    break;
            }
        }

        private void UpdateDiskUsage(Server server, Random random)
        {
            // Leichte Änderungen der Festplatten-Auslastung simulieren
            foreach (var partition in server.DiskPartitions)
            {
                var change = (random.NextDouble() - 0.5) * 2; // -1 bis +1 GB Änderung
                partition.UsedSpaceGB = Math.Max(0, Math.Min(partition.TotalSpaceGB * 0.95, partition.UsedSpaceGB + change));
            }
        }
    }
}
