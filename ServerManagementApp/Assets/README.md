# Assets Folder

Diese Ordner sollte die folgenden Bilddateien enthalten:

## Erforderliche Assets:
- **SplashScreen.scale-200.png** (620x300 px) - Splash Screen beim App-Start
- **LockScreenLogo.scale-200.png** (48x48 px) - Logo für den Sperrbildschirm
- **Square150x150Logo.scale-200.png** (300x300 px) - <PERSON><PERSON><PERSON>-Logo
- **Square44x44Logo.scale-200.png** (88x88 px) - <PERSON><PERSON>-Logo
- **Square44x44Logo.targetsize-24_altform-unplated.png** (24x24 px) - Unplated Logo
- **StoreLogo.png** (50x50 px) - Store Logo
- **Wide310x150Logo.scale-200.png** (620x300 px) - Breites Kachel-Logo

## Hinweise:
- Alle Bilder sollten im PNG-Format sein
- Die Größenangaben sind für scale-200 (200% DPI)
- Für eine vollständige App sollten Sie auch scale-100, scale-125, scale-150, scale-400 Versionen erstellen
- Sie können diese Placeholder-Dateien durch echte Bilder ersetzen

## Temporäre Lösung:
Für den ersten Test können Sie einfache einfarbige PNG-Dateien mit den entsprechenden Namen und Größen erstellen.
