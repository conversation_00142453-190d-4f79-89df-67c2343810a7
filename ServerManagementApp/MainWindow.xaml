<Window
    x:Class="ServerManagementApp.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:ServerManagementApp"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="Server Management App" Height="900" Width="1600"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    UseLayoutRounding="True"
    TextOptions.TextFormattingMode="Display"
    TextOptions.TextRenderingMode="ClearType"
    RenderOptions.BitmapScalingMode="HighQuality">

    <Window.Resources>
        <local:PercentToWidthConverter x:Key="PercentToWidthConverter"/>
        <local:PercentToColorConverter x:Key="PercentToColorConverter"/>
    </Window.Resources>

    <Grid Background="#F8FAFC">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Dashboard Header -->
        <Border Grid.Row="0" Background="#1E293B" Padding="32,24">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="Haug Components"
                               FontSize="28"
                               FontWeight="Bold"
                               Foreground="White"
                               Margin="0,0,0,4"/>
                    <TextBlock Text="Server Infrastructure Dashboard"
                               FontSize="16"
                               Foreground="#94A3B8"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Border Background="#0F172A"
                            CornerRadius="8"
                            Padding="16,12"
                            Margin="0,0,16,0">
                        <StackPanel>
                            <TextBlock Text="Last Update"
                                       FontSize="12"
                                       Foreground="#64748B"
                                       Margin="0,0,0,4"/>
                            <TextBlock x:Name="LastUpdateTextBlock"
                                       Text="Just now"
                                       FontSize="14"
                                       FontWeight="SemiBold"
                                       Foreground="White"/>
                        </StackPanel>
                    </Border>

                    <Border Background="#0F172A"
                            CornerRadius="8"
                            Padding="16,12">
                        <StackPanel>
                            <TextBlock Text="Current Time"
                                       FontSize="12"
                                       Foreground="#64748B"
                                       Margin="0,0,0,4"/>
                            <TextBlock x:Name="TimeTextBlock"
                                       Text="11:30:00"
                                       FontSize="14"
                                       FontWeight="SemiBold"
                                       Foreground="White"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Dashboard Statistics -->
        <Border Grid.Row="1" Background="White" Padding="32,24" BorderBrush="#E2E8F0" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Total Servers -->
                <Border Grid.Column="0" Background="#F1F5F9" CornerRadius="12" Padding="20,16" Margin="0,0,12,0">
                    <StackPanel>
                        <TextBlock Text="📊" FontSize="24" Margin="0,0,0,8"/>
                        <TextBlock x:Name="TotalServersText" Text="7" FontSize="32" FontWeight="Bold" Foreground="#1E293B" Margin="0,0,0,4"/>
                        <TextBlock Text="Total Servers" FontSize="14" Foreground="#64748B"/>
                    </StackPanel>
                </Border>

                <!-- Online Servers -->
                <Border Grid.Column="1" Background="#DCFCE7" CornerRadius="12" Padding="20,16" Margin="6,0">
                    <StackPanel>
                        <TextBlock Text="✅" FontSize="24" Margin="0,0,0,8"/>
                        <TextBlock x:Name="OnlineServersText" Text="5" FontSize="32" FontWeight="Bold" Foreground="#166534" Margin="0,0,0,4"/>
                        <TextBlock Text="Online" FontSize="14" Foreground="#16A34A"/>
                    </StackPanel>
                </Border>

                <!-- Offline Servers -->
                <Border Grid.Column="2" Background="#FEE2E2" CornerRadius="12" Padding="20,16" Margin="6,0">
                    <StackPanel>
                        <TextBlock Text="❌" FontSize="24" Margin="0,0,0,8"/>
                        <TextBlock x:Name="OfflineServersText" Text="2" FontSize="32" FontWeight="Bold" Foreground="#991B1B" Margin="0,0,0,4"/>
                        <TextBlock Text="Offline" FontSize="14" Foreground="#DC2626"/>
                    </StackPanel>
                </Border>

                <!-- Average Response -->
                <Border Grid.Column="3" Background="#FEF3C7" CornerRadius="12" Padding="20,16" Margin="6,0,12,0">
                    <StackPanel>
                        <TextBlock Text="⚡" FontSize="24" Margin="0,0,0,8"/>
                        <TextBlock x:Name="AvgResponseText" Text="12ms" FontSize="32" FontWeight="Bold" Foreground="#92400E" Margin="0,0,0,4"/>
                        <TextBlock Text="Avg Response" FontSize="14" Foreground="#D97706"/>
                    </StackPanel>
                </Border>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="4" Orientation="Horizontal">
                    <Border Background="#3B82F6" CornerRadius="8" Margin="0,0,12,0">
                        <Button Content="🔄 Refresh All"
                                Background="Transparent"
                                Foreground="White"
                                Padding="20,12"
                                FontWeight="SemiBold"
                                BorderThickness="0"
                                Click="RefreshAllButton_Click"/>
                    </Border>
                    <Border Background="#10B981" CornerRadius="8">
                        <Button Content="➕ Add Server"
                                Background="Transparent"
                                Foreground="White"
                                Padding="20,12"
                                FontWeight="SemiBold"
                                BorderThickness="0"
                                Click="AddServerButton_Click"/>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Server Grid -->
        <ScrollViewer Grid.Row="2" Padding="32,24">
            <StackPanel>
                <TextBlock Text="Server Overview"
                           FontSize="24"
                           FontWeight="Bold"
                           Foreground="#1E293B"
                           Margin="0,0,0,24"/>

                <!-- Server Grid -->
                <ItemsControl x:Name="ServerList">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <UniformGrid Columns="3" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="White"
                                    CornerRadius="16"
                                    Padding="0"
                                    Margin="12"
                                    MinHeight="320"
                                    BorderBrush="#E2E8F0"
                                    BorderThickness="1"
                                    UseLayoutRounding="True"
                                    SnapsToDevicePixels="True">
                                <Border.Effect>
                                    <DropShadowEffect Color="#64748B" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="12"/>
                                </Border.Effect>

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Server Header -->
                                    <Border Grid.Row="0"
                                            Background="#F8FAFC"
                                            CornerRadius="16,16,0,0"
                                            Padding="24,20"
                                            UseLayoutRounding="True">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Server Icon -->
                                            <Border Grid.Column="0"
                                                    Background="#3B82F6"
                                                    CornerRadius="12"
                                                    Width="48"
                                                    Height="48"
                                                    Margin="0,0,16,0">
                                                <TextBlock Text="🖥️"
                                                           FontSize="24"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                            </Border>

                                            <!-- Server Info -->
                                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding Name}"
                                                           FontWeight="Bold"
                                                           FontSize="20"
                                                           Foreground="#1E293B"
                                                           Margin="0,0,0,4"/>
                                                <TextBlock Text="{Binding Description}"
                                                           FontSize="14"
                                                           Foreground="#64748B"/>
                                            </StackPanel>

                                            <!-- Status Badge -->
                                            <Border Grid.Column="2"
                                                    CornerRadius="8"
                                                    Padding="12,6"
                                                    Background="{Binding StatusColor}"
                                                    VerticalAlignment="Top"
                                                    UseLayoutRounding="True">
                                                <TextBlock Text="{Binding StatusText}"
                                                           Foreground="White"
                                                           FontWeight="SemiBold"
                                                           FontSize="12"/>
                                            </Border>
                                        </Grid>
                                    </Border>

                                    <!-- Content Area -->
                                    <StackPanel Grid.Row="1" Margin="24,20">

                                        <!-- Network & Connection Info -->
                                        <Border Background="#F1F5F9" CornerRadius="12" Padding="16,12" Margin="0,0,0,16">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBlock Grid.Column="0"
                                                           Text="🌐"
                                                           FontSize="16"
                                                           Margin="0,0,12,0"
                                                           VerticalAlignment="Center"/>

                                                <TextBlock Grid.Column="1"
                                                           Text="{Binding IpAddress}"
                                                           FontSize="16"
                                                           FontWeight="SemiBold"
                                                           Foreground="#1E293B"
                                                           VerticalAlignment="Center"/>

                                                <TextBlock Grid.Column="2"
                                                           Text="{Binding PingTimeText}"
                                                           FontSize="12"
                                                           Foreground="#64748B"
                                                           VerticalAlignment="Center"/>
                                            </Grid>
                                        </Border>

                                        <!-- System Resources -->
                                        <Grid Margin="0,0,0,16">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- CPU Usage -->
                                            <Border Grid.Column="0"
                                                    Background="#FEF3C7"
                                                    CornerRadius="12"
                                                    Padding="16,12"
                                                    Margin="0,0,8,0"
                                                    UseLayoutRounding="True">
                                                <StackPanel>
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                        </Grid.ColumnDefinitions>
                                                        <TextBlock Grid.Column="0" Text="💻" FontSize="16" Margin="0,0,8,0"/>
                                                        <TextBlock Grid.Column="1" Text="CPU Usage"
                                                                   FontSize="12"
                                                                   Foreground="#D97706"
                                                                   FontWeight="SemiBold"/>
                                                    </Grid>
                                                    <TextBlock Text="{Binding CpuUsage, StringFormat={}{0}%}"
                                                               FontSize="24"
                                                               FontWeight="Bold"
                                                               Foreground="#92400E"
                                                               Margin="0,8,0,0"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- Memory Usage -->
                                            <Border Grid.Column="1"
                                                    Background="#D1FAE5"
                                                    CornerRadius="12"
                                                    Padding="16,12"
                                                    Margin="8,0,0,0"
                                                    UseLayoutRounding="True">
                                                <StackPanel>
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                        </Grid.ColumnDefinitions>
                                                        <TextBlock Grid.Column="0" Text="🧠" FontSize="16" Margin="0,0,8,0"/>
                                                        <TextBlock Grid.Column="1" Text="Memory Usage"
                                                                   FontSize="12"
                                                                   Foreground="#059669"
                                                                   FontWeight="SemiBold"/>
                                                    </Grid>
                                                    <TextBlock Text="{Binding MemoryUsageText}"
                                                               FontSize="14"
                                                               FontWeight="SemiBold"
                                                               Foreground="#047857"
                                                               TextWrapping="Wrap"
                                                               Margin="0,8,0,0"/>
                                                </StackPanel>
                                            </Border>
                                        </Grid>

                                        <!-- Disk Storage -->
                                        <Border Background="#EDE9FE"
                                                CornerRadius="12"
                                                Padding="16,12"
                                                UseLayoutRounding="True">
                                            <StackPanel>
                                                <Grid Margin="0,0,0,12">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Grid.Column="0" Text="💾" FontSize="16" Margin="0,0,8,0"/>
                                                    <TextBlock Grid.Column="1" Text="Storage Overview"
                                                               FontSize="12"
                                                               Foreground="#7C3AED"
                                                               FontWeight="SemiBold"/>
                                                </Grid>

                                                <ItemsControl ItemsSource="{Binding DiskPartitions}">
                                                    <ItemsControl.ItemTemplate>
                                                        <DataTemplate>
                                                            <Border Background="White"
                                                                    CornerRadius="8"
                                                                    Padding="12,8"
                                                                    Margin="0,0,0,6">
                                                                <Grid>
                                                                    <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                    </Grid.ColumnDefinitions>

                                                                    <!-- Drive Letter -->
                                                                    <Border Grid.Column="0"
                                                                            Background="#7C3AED"
                                                                            CornerRadius="6"
                                                                            Width="32"
                                                                            Height="32"
                                                                            Margin="0,0,12,0">
                                                                        <TextBlock Text="{Binding DriveLetter}"
                                                                                   FontSize="14"
                                                                                   FontWeight="Bold"
                                                                                   Foreground="White"
                                                                                   HorizontalAlignment="Center"
                                                                                   VerticalAlignment="Center"/>
                                                                    </Border>

                                                                    <!-- Drive Info -->
                                                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                                        <TextBlock Text="{Binding Label}"
                                                                                   FontSize="12"
                                                                                   FontWeight="SemiBold"
                                                                                   Foreground="#1E293B"
                                                                                   Margin="0,0,0,2"/>
                                                                        <TextBlock Text="{Binding DisplayText}"
                                                                                   FontSize="11"
                                                                                   Foreground="#64748B"/>
                                                                    </StackPanel>

                                                                    <!-- Usage Percentage -->
                                                                    <Border Grid.Column="2"
                                                                            Background="{Binding UsageColor}"
                                                                            CornerRadius="12"
                                                                            Padding="8,4"
                                                                            VerticalAlignment="Center">
                                                                        <TextBlock Text="{Binding UsagePercent, StringFormat={}{0:F0}%}"
                                                                                   FontSize="11"
                                                                                   FontWeight="Bold"
                                                                                   Foreground="White"/>
                                                                    </Border>
                                                                </Grid>
                                                            </Border>
                                                        </DataTemplate>
                                                    </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>

                                    <!-- Footer -->
                                    <Border Grid.Row="2"
                                            Background="#F8FAFC"
                                            CornerRadius="0,0,16,16"
                                            Padding="24,12"
                                            UseLayoutRounding="True">
                                        <TextBlock Text="{Binding LastPingText}"
                                                   FontSize="11"
                                                   Foreground="#94A3B8"
                                                   HorizontalAlignment="Center"/>
                                    </Border>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>

        <!-- Dashboard Footer -->
        <Border Grid.Row="3"
                Background="#1E293B"
                Padding="32,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Text="© 2025 Haug Components - Server Infrastructure Management"
                           FontSize="12"
                           Foreground="#64748B"
                           VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="Auto-refresh: 30s"
                               FontSize="12"
                               Foreground="#64748B"
                               VerticalAlignment="Center"
                               Margin="0,0,16,0"/>
                    <Border Background="#0F172A"
                            CornerRadius="6"
                            Padding="8,4">
                        <TextBlock Text="🟢 System Active"
                                   FontSize="12"
                                   Foreground="#10B981"
                                   FontWeight="SemiBold"/>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
