<Window x:Class="ServerManagementApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ServerManagementApp"
        mc:Ignorable="d"
        Title="Haug Components - Server Management Dashboard"
        Height="900" Width="1400"
        WindowState="Maximized"
        Background="#020817">

    <Grid Background="#020817">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Modern Header -->
        <Border Grid.Row="0" Background="#020817" Padding="24,16" BorderBrush="#1E293B" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="Haug Components" 
                               FontSize="24" 
                               FontWeight="SemiBold" 
                               Foreground="#F8FAFC"
                               Margin="0,0,16,0"/>
                    <Border Background="#1E293B" 
                            CornerRadius="6" 
                            Padding="8,4">
                        <TextBlock Text="Dashboard" 
                                   FontSize="12" 
                                   FontWeight="Medium"
                                   Foreground="#94A3B8"/>
                    </Border>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Background="#0F172A" 
                            BorderBrush="#374151"
                            BorderThickness="1"
                            Padding="12,8"
                            Margin="0,0,8,0"
                            Click="RefreshAllButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="↻" FontSize="14" Margin="0,0,6,0" Foreground="#94A3B8"/>
                            <TextBlock Text="Refresh" FontSize="12" FontWeight="Medium" Foreground="#F8FAFC"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Background="#1F2937" 
                            BorderBrush="#374151"
                            BorderThickness="1"
                            Padding="12,8"
                            Click="AddServerButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="+" FontSize="14" Margin="0,0,6,0" Foreground="#94A3B8"/>
                            <TextBlock Text="Add Server" FontSize="12" FontWeight="Medium" Foreground="#F8FAFC"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" Background="#020817" Padding="24">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- Stats Cards -->
                <Grid Grid.Row="0" Margin="0,0,0,24">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Total Servers Card -->
                    <Border Grid.Column="0" Background="#0F172A" CornerRadius="8" Padding="20,16" Margin="0,0,12,0" BorderBrush="#1E293B" BorderThickness="1">
                        <StackPanel>
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Total Servers" FontSize="14" FontWeight="Medium" Foreground="#94A3B8"/>
                                <Border Grid.Column="1" Background="#1E293B" CornerRadius="4" Padding="6,2">
                                    <TextBlock Text="🖥️" FontSize="12"/>
                                </Border>
                            </Grid>
                            <TextBlock x:Name="TotalServersText" Text="7" FontSize="32" FontWeight="Bold" Foreground="#F8FAFC" Margin="0,0,0,4"/>
                            <TextBlock Text="+2 from last month" FontSize="12" Foreground="#10B981"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Online Servers Card -->
                    <Border Grid.Column="1" Background="#0F172A" CornerRadius="8" Padding="20,16" Margin="6,0" BorderBrush="#1E293B" BorderThickness="1">
                        <StackPanel>
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Online" FontSize="14" FontWeight="Medium" Foreground="#94A3B8"/>
                                <Border Grid.Column="1" Background="#1E293B" CornerRadius="4" Padding="6,2">
                                    <TextBlock Text="✓" FontSize="12" Foreground="#10B981"/>
                                </Border>
                            </Grid>
                            <TextBlock x:Name="OnlineServersText" Text="5" FontSize="32" FontWeight="Bold" Foreground="#F8FAFC" Margin="0,0,0,4"/>
                            <TextBlock Text="71% uptime" FontSize="12" Foreground="#10B981"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Offline Servers Card -->
                    <Border Grid.Column="2" Background="#0F172A" CornerRadius="8" Padding="20,16" Margin="6,0" BorderBrush="#1E293B" BorderThickness="1">
                        <StackPanel>
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Issues" FontSize="14" FontWeight="Medium" Foreground="#94A3B8"/>
                                <Border Grid.Column="1" Background="#1E293B" CornerRadius="4" Padding="6,2">
                                    <TextBlock Text="⚠" FontSize="12" Foreground="#F59E0B"/>
                                </Border>
                            </Grid>
                            <TextBlock x:Name="OfflineServersText" Text="2" FontSize="32" FontWeight="Bold" Foreground="#F8FAFC" Margin="0,0,0,4"/>
                            <TextBlock Text="Needs attention" FontSize="12" Foreground="#F59E0B"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Response Time Card -->
                    <Border Grid.Column="3" Background="#0F172A" CornerRadius="8" Padding="20,16" Margin="12,0,0,0" BorderBrush="#1E293B" BorderThickness="1">
                        <StackPanel>
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Avg Response" FontSize="14" FontWeight="Medium" Foreground="#94A3B8"/>
                                <Border Grid.Column="1" Background="#1E293B" CornerRadius="4" Padding="6,2">
                                    <TextBlock Text="⚡" FontSize="12" Foreground="#3B82F6"/>
                                </Border>
                            </Grid>
                            <TextBlock x:Name="AvgResponseText" Text="12ms" FontSize="32" FontWeight="Bold" Foreground="#F8FAFC" Margin="0,0,0,4"/>
                            <TextBlock Text="-5ms from last hour" FontSize="12" Foreground="#10B981"/>
                        </StackPanel>
                    </Border>
                </Grid>
                
                <!-- Section Header -->
                <Grid Grid.Row="1" Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="Servers" FontSize="20" FontWeight="SemiBold" Foreground="#F8FAFC" VerticalAlignment="Center"/>
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Background="Transparent" BorderBrush="#374151" BorderThickness="1" Padding="8,6" Margin="0,0,8,0">
                            <TextBlock Text="Filter" FontSize="12" Foreground="#94A3B8"/>
                        </Button>
                        <Button Background="Transparent" BorderBrush="#374151" BorderThickness="1" Padding="8,6">
                            <TextBlock Text="Sort" FontSize="12" Foreground="#94A3B8"/>
                        </Button>
                    </StackPanel>
                </Grid>

                <!-- Server Cards -->
                <ItemsControl x:Name="ServerList" Grid.Row="2">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="#0F172A"
                                    CornerRadius="8"
                                    Margin="0,0,0,12"
                                    BorderBrush="#1E293B"
                                    BorderThickness="1"
                                    UseLayoutRounding="True"
                                    SnapsToDevicePixels="True">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="BorderBrush" Value="#374151"/>
                                                <Setter Property="Background" Value="#111827"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>

                                <!-- Server Card Content -->
                                <Grid Margin="20,16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <!-- Header Row -->
                                    <Grid Grid.Row="0" Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Server Icon & Info -->
                                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                                            <Border Background="#1E293B" 
                                                    CornerRadius="8" 
                                                    Width="48" 
                                                    Height="48"
                                                    Margin="0,0,12,0">
                                                <TextBlock Text="💻" 
                                                           FontSize="20" 
                                                           HorizontalAlignment="Center" 
                                                           VerticalAlignment="Center"/>
                                            </Border>
                                            
                                            <StackPanel VerticalAlignment="Center">
                                                <TextBlock Text="{Binding Name}" 
                                                           FontWeight="SemiBold" 
                                                           FontSize="16"
                                                           Foreground="#F8FAFC"
                                                           Margin="0,0,0,2"/>
                                                <TextBlock Text="{Binding Description}" 
                                                           FontSize="12"
                                                           Foreground="#94A3B8"/>
                                            </StackPanel>
                                        </StackPanel>
                                        
                                        <!-- Status Badge -->
                                        <Border Grid.Column="2" 
                                                Background="{Binding StatusColor}" 
                                                CornerRadius="6" 
                                                Padding="8,4"
                                                Margin="0,0,12,0">
                                            <TextBlock Text="{Binding StatusText}" 
                                                       FontSize="12" 
                                                       FontWeight="Medium"
                                                       Foreground="White"/>
                                        </Border>
                                        
                                        <!-- Action Buttons -->
                                        <StackPanel Grid.Column="3" Orientation="Horizontal">
                                            <Button Background="Transparent"
                                                    BorderBrush="#374151"
                                                    BorderThickness="1"
                                                    Padding="8,6"
                                                    Margin="0,0,4,0"
                                                    ToolTip="Refresh Server"
                                                    Click="RefreshServerButton_Click"
                                                    Tag="{Binding}">
                                                <TextBlock Text="↻" FontSize="14" Foreground="#94A3B8"/>
                                            </Button>

                                            <Button Background="Transparent"
                                                    BorderBrush="#374151"
                                                    BorderThickness="1"
                                                    Padding="8,6"
                                                    Margin="0,0,4,0"
                                                    ToolTip="Restart Server"
                                                    Click="RestartServerButton_Click"
                                                    Tag="{Binding}">
                                                <TextBlock Text="⟲" FontSize="14" Foreground="#94A3B8"/>
                                            </Button>

                                            <Button Background="Transparent"
                                                    BorderBrush="#374151"
                                                    BorderThickness="1"
                                                    Padding="8,6"
                                                    ToolTip="Delete Server"
                                                    Click="DeleteServerButton_Click"
                                                    Tag="{Binding}">
                                                <TextBlock Text="✕" FontSize="14" Foreground="#EF4444"/>
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                    
                                    <!-- Metrics Row -->
                                    <Grid Grid.Row="1" Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <!-- IP Address -->
                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="IP Address" FontSize="11" Foreground="#6B7280" Margin="0,0,0,4"/>
                                            <TextBlock Text="{Binding IpAddress}" FontSize="14" FontWeight="Medium" Foreground="#F8FAFC"/>
                                        </StackPanel>
                                        
                                        <!-- CPU Usage -->
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="CPU Usage" FontSize="11" Foreground="#6B7280" Margin="0,0,0,4"/>
                                            <TextBlock Text="{Binding CpuUsage, StringFormat={}{0}%}" FontSize="14" FontWeight="Medium" Foreground="#F8FAFC"/>
                                        </StackPanel>
                                        
                                        <!-- Uptime -->
                                        <StackPanel Grid.Column="2">
                                            <TextBlock Text="Uptime" FontSize="11" Foreground="#6B7280" Margin="0,0,0,4"/>
                                            <TextBlock Text="{Binding UptimeText}" FontSize="14" FontWeight="Medium" Foreground="#F8FAFC"/>
                                        </StackPanel>
                                        
                                        <!-- OS Version -->
                                        <StackPanel Grid.Column="3">
                                            <TextBlock Text="OS Version" FontSize="11" Foreground="#6B7280" Margin="0,0,0,4"/>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="{Binding OsIcon}" FontSize="12" Margin="0,0,4,0"/>
                                                <TextBlock Text="{Binding OsVersion}" FontSize="14" FontWeight="Medium" Foreground="#F8FAFC"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </Grid>
                                    
                                    <!-- Alerts Row -->
                                    <Border Grid.Row="2" 
                                            Background="{Binding AlertBackgroundColor}" 
                                            CornerRadius="6" 
                                            Padding="12,8"
                                            BorderBrush="#374151"
                                            BorderThickness="1">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" 
                                                       Text="{Binding AlertIcon}" 
                                                       FontSize="14" 
                                                       Margin="0,0,8,0"/>
                                            
                                            <TextBlock Grid.Column="1" 
                                                       Text="{Binding AlertText}" 
                                                       FontSize="12" 
                                                       FontWeight="Medium"
                                                       Foreground="{Binding AlertTextColor}"
                                                       VerticalAlignment="Center"/>
                                            
                                            <TextBlock Grid.Column="2" 
                                                       Text="{Binding PingTimeText}" 
                                                       FontSize="11" 
                                                       Foreground="#6B7280"
                                                       VerticalAlignment="Center"/>
                                        </Grid>
                                    </Border>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </Grid>
        </ScrollViewer>
    </Grid>
</Window>
