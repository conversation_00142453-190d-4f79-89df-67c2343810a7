<Window
    x:Class="ServerManagementApp.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:ServerManagementApp"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="Server Management App" Height="900" Width="1600"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    UseLayoutRounding="True"
    TextOptions.TextFormattingMode="Display"
    TextOptions.TextRenderingMode="ClearType"
    RenderOptions.BitmapScalingMode="HighQuality">

    <Window.Resources>
        <local:PercentToWidthConverter x:Key="PercentToWidthConverter"/>
        <local:PercentToColorConverter x:Key="PercentToColorConverter"/>
    </Window.Resources>

    <Grid Background="#F8FAFC">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Dashboard Header -->
        <Border Grid.Row="0" Background="#1E293B" Padding="32,24">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="Haug Components"
                               FontSize="32"
                               FontWeight="ExtraLight"
                               Foreground="White"
                               Margin="0,0,0,6"/>
                    <TextBlock Text="Server-Infrastruktur Dashboard"
                               FontSize="18"
                               FontWeight="Light"
                               Foreground="#CBD5E1"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Border Background="#0F172A"
                            CornerRadius="8"
                            Padding="16,12"
                            Margin="0,0,16,0">
                        <StackPanel>
                            <TextBlock Text="Letzte Aktualisierung"
                                       FontSize="11"
                                       FontWeight="Medium"
                                       Foreground="#64748B"
                                       Margin="0,0,0,4"/>
                            <TextBlock x:Name="LastUpdateTextBlock"
                                       Text="Gerade eben"
                                       FontSize="15"
                                       FontWeight="SemiBold"
                                       Foreground="White"/>
                        </StackPanel>
                    </Border>

                    <Border Background="#0F172A"
                            CornerRadius="8"
                            Padding="16,12">
                        <StackPanel>
                            <TextBlock Text="Aktuelle Zeit"
                                       FontSize="11"
                                       FontWeight="Medium"
                                       Foreground="#64748B"
                                       Margin="0,0,0,4"/>
                            <TextBlock x:Name="TimeTextBlock"
                                       Text="11:30:00"
                                       FontSize="15"
                                       FontWeight="SemiBold"
                                       Foreground="White"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Dashboard Statistics -->
        <Border Grid.Row="1" Background="White" Padding="32,24" BorderBrush="#E2E8F0" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Gesamt Server -->
                <Border Grid.Column="0" Background="White" CornerRadius="16" Padding="24,20" Margin="0,0,16,0" BorderBrush="#E2E8F0" BorderThickness="1">
                    <StackPanel>
                        <Border Background="#F1F5F9" CornerRadius="12" Width="48" Height="48" Margin="0,0,0,12">
                            <TextBlock Text="🖥️" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="TotalServersText" Text="7" FontSize="36" FontWeight="ExtraLight" Foreground="#0F172A" Margin="0,0,0,4"/>
                        <TextBlock Text="Gesamt Server" FontSize="13" FontWeight="Medium" Foreground="#64748B"/>
                    </StackPanel>
                </Border>

                <!-- Online Server -->
                <Border Grid.Column="1" Background="White" CornerRadius="16" Padding="24,20" Margin="8,0" BorderBrush="#10B981" BorderThickness="2">
                    <StackPanel>
                        <Border Background="#DCFCE7" CornerRadius="12" Width="48" Height="48" Margin="0,0,0,12">
                            <TextBlock Text="✅" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="OnlineServersText" Text="5" FontSize="36" FontWeight="ExtraLight" Foreground="#059669" Margin="0,0,0,4"/>
                        <TextBlock Text="Online" FontSize="13" FontWeight="Medium" Foreground="#10B981"/>
                    </StackPanel>
                </Border>

                <!-- Offline Server -->
                <Border Grid.Column="2" Background="White" CornerRadius="16" Padding="24,20" Margin="8,0" BorderBrush="#EF4444" BorderThickness="2">
                    <StackPanel>
                        <Border Background="#FEE2E2" CornerRadius="12" Width="48" Height="48" Margin="0,0,0,12">
                            <TextBlock Text="⚠️" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="OfflineServersText" Text="2" FontSize="36" FontWeight="ExtraLight" Foreground="#DC2626" Margin="0,0,0,4"/>
                        <TextBlock Text="Offline" FontSize="13" FontWeight="Medium" Foreground="#EF4444"/>
                    </StackPanel>
                </Border>

                <!-- Durchschnittliche Antwortzeit -->
                <Border Grid.Column="3" Background="White" CornerRadius="16" Padding="24,20" Margin="8,0,16,0" BorderBrush="#F59E0B" BorderThickness="2">
                    <StackPanel>
                        <Border Background="#FEF3C7" CornerRadius="12" Width="48" Height="48" Margin="0,0,0,12">
                            <TextBlock Text="⚡" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="AvgResponseText" Text="12ms" FontSize="36" FontWeight="ExtraLight" Foreground="#D97706" Margin="0,0,0,4"/>
                        <TextBlock Text="Ø Antwortzeit" FontSize="13" FontWeight="Medium" Foreground="#F59E0B"/>
                    </StackPanel>
                </Border>

                <!-- Aktions-Buttons -->
                <StackPanel Grid.Column="4" Orientation="Horizontal">
                    <Border Background="#3B82F6" CornerRadius="12" Margin="0,0,16,0">
                        <Button Content="🔄 Alle aktualisieren"
                                Background="Transparent"
                                Foreground="White"
                                Padding="24,16"
                                FontWeight="Medium"
                                FontSize="14"
                                BorderThickness="0"
                                Click="RefreshAllButton_Click"/>
                    </Border>
                    <Border Background="#10B981" CornerRadius="12">
                        <Button Content="➕ Server hinzufügen"
                                Background="Transparent"
                                Foreground="White"
                                Padding="24,16"
                                FontWeight="Medium"
                                FontSize="14"
                                BorderThickness="0"
                                Click="AddServerButton_Click"/>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Server Grid -->
        <ScrollViewer Grid.Row="2" Padding="32,24">
            <StackPanel>
                <TextBlock Text="Server-Übersicht"
                           FontSize="28"
                           FontWeight="Light"
                           Foreground="#0F172A"
                           Margin="0,0,0,32"/>

                <!-- Server Akkordeon -->
                <ItemsControl x:Name="ServerList">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="White"
                                    CornerRadius="16"
                                    Margin="0,0,0,16"
                                    BorderBrush="#E2E8F0"
                                    BorderThickness="1"
                                    UseLayoutRounding="True"
                                    SnapsToDevicePixels="True">
                                <Border.Effect>
                                    <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="2" Opacity="0.06" BlurRadius="16"/>
                                </Border.Effect>

                                <Expander Background="Transparent"
                                          BorderThickness="0"
                                          IsExpanded="False">

                                <!-- Akkordeon Header -->
                                <Expander.Header>
                                    <Border Background="Transparent"
                                            Padding="24,20"
                                            UseLayoutRounding="True">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Server Icon -->
                                            <Border Grid.Column="0"
                                                    Background="#6366F1"
                                                    CornerRadius="12"
                                                    Width="48"
                                                    Height="48"
                                                    Margin="0,0,20,0">
                                                <TextBlock Text="🖥️"
                                                           FontSize="24"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                            </Border>

                                            <!-- Server Info -->
                                            <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="0,0,24,0">
                                                <TextBlock Text="{Binding Name}"
                                                           FontWeight="SemiBold"
                                                           FontSize="18"
                                                           Foreground="#0F172A"
                                                           Margin="0,0,0,4"/>
                                                <TextBlock Text="{Binding Description}"
                                                           FontSize="13"
                                                           FontWeight="Medium"
                                                           Foreground="#64748B"/>
                                            </StackPanel>

                                            <!-- IP Address -->
                                            <Border Grid.Column="2"
                                                    Background="#F1F5F9"
                                                    CornerRadius="8"
                                                    Padding="12,6"
                                                    VerticalAlignment="Center"
                                                    Margin="0,0,16,0">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="🌐" FontSize="14" Margin="0,0,8,0"/>
                                                    <TextBlock Text="{Binding IpAddress}"
                                                               FontSize="13"
                                                               FontWeight="Medium"
                                                               Foreground="#374151"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- CPU -->
                                            <Border Grid.Column="3"
                                                    Background="#FEF3C7"
                                                    CornerRadius="8"
                                                    Padding="12,6"
                                                    VerticalAlignment="Center"
                                                    Margin="0,0,12,0">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="💻" FontSize="14" Margin="0,0,6,0"/>
                                                    <TextBlock Text="{Binding CpuUsage, StringFormat={}{0}%}"
                                                               FontSize="13"
                                                               FontWeight="SemiBold"
                                                               Foreground="#D97706"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- Memory -->
                                            <Border Grid.Column="4"
                                                    Background="#D1FAE5"
                                                    CornerRadius="8"
                                                    Padding="12,6"
                                                    VerticalAlignment="Center"
                                                    Margin="0,0,12,0">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="🧠" FontSize="14" Margin="0,0,6,0"/>
                                                    <TextBlock Text="{Binding MemoryUsageText}"
                                                               FontSize="11"
                                                               FontWeight="SemiBold"
                                                               Foreground="#059669"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- Ping -->
                                            <Border Grid.Column="5"
                                                    Background="#E0E7FF"
                                                    CornerRadius="8"
                                                    Padding="12,6"
                                                    VerticalAlignment="Center"
                                                    Margin="0,0,16,0">
                                                <TextBlock Text="{Binding PingTimeText}"
                                                           FontSize="12"
                                                           FontWeight="Medium"
                                                           Foreground="#4338CA"/>
                                            </Border>

                                            <!-- Status Badge -->
                                            <Border Grid.Column="6"
                                                    CornerRadius="10"
                                                    Padding="16,8"
                                                    Background="{Binding StatusColor}"
                                                    VerticalAlignment="Center"
                                                    UseLayoutRounding="True">
                                                <TextBlock Text="{Binding StatusText}"
                                                           Foreground="White"
                                                           FontWeight="SemiBold"
                                                           FontSize="12"/>
                                            </Border>
                                        </Grid>
                                    </Border>
                                </Expander.Header>

                                <!-- Akkordeon Content -->
                                <Border Background="#FAFBFC"
                                        CornerRadius="0,0,16,16"
                                        Padding="32,24"
                                        BorderBrush="#E2E8F0"
                                        BorderThickness="0,1,0,0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Linke Spalte: System-Ressourcen -->
                                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                                            <TextBlock Text="📊 System-Ressourcen"
                                                       FontSize="16"
                                                       FontWeight="SemiBold"
                                                       Foreground="#0F172A"
                                                       Margin="0,0,0,16"/>

                                            <!-- CPU Details -->
                                            <Border Background="White"
                                                    CornerRadius="12"
                                                    Padding="20,16"
                                                    Margin="0,0,0,12"
                                                    BorderBrush="#FEF3C7"
                                                    BorderThickness="2">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <Border Grid.Column="0"
                                                            Background="#FEF3C7"
                                                            CornerRadius="10"
                                                            Width="40"
                                                            Height="40"
                                                            Margin="0,0,16,0">
                                                        <TextBlock Text="💻"
                                                                   FontSize="20"
                                                                   HorizontalAlignment="Center"
                                                                   VerticalAlignment="Center"/>
                                                    </Border>

                                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                        <TextBlock Text="CPU-Auslastung"
                                                                   FontSize="14"
                                                                   FontWeight="SemiBold"
                                                                   Foreground="#0F172A"
                                                                   Margin="0,0,0,4"/>
                                                        <TextBlock Text="Prozessor-Aktivität"
                                                                   FontSize="12"
                                                                   Foreground="#64748B"/>
                                                    </StackPanel>

                                                    <TextBlock Grid.Column="2"
                                                               Text="{Binding CpuUsage, StringFormat={}{0}%}"
                                                               FontSize="24"
                                                               FontWeight="Bold"
                                                               Foreground="#D97706"
                                                               VerticalAlignment="Center"/>
                                                </Grid>
                                            </Border>

                                            <!-- Memory Details -->
                                            <Border Background="White"
                                                    CornerRadius="12"
                                                    Padding="20,16"
                                                    Margin="0,0,0,12"
                                                    BorderBrush="#D1FAE5"
                                                    BorderThickness="2">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>

                                                    <Border Grid.Column="0"
                                                            Background="#D1FAE5"
                                                            CornerRadius="10"
                                                            Width="40"
                                                            Height="40"
                                                            Margin="0,0,16,0">
                                                        <TextBlock Text="🧠"
                                                                   FontSize="20"
                                                                   HorizontalAlignment="Center"
                                                                   VerticalAlignment="Center"/>
                                                    </Border>

                                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                        <TextBlock Text="Arbeitsspeicher"
                                                                   FontSize="14"
                                                                   FontWeight="SemiBold"
                                                                   Foreground="#0F172A"
                                                                   Margin="0,0,0,4"/>
                                                        <TextBlock Text="{Binding MemoryUsageText}"
                                                                   FontSize="13"
                                                                   FontWeight="Medium"
                                                                   Foreground="#059669"/>
                                                    </StackPanel>
                                                </Grid>
                                            </Border>
                                        </StackPanel>

                                        <!-- Rechte Spalte: Festplatten-Speicher -->
                                        <StackPanel Grid.Column="1" Margin="20,0,0,0">
                                            <TextBlock Text="💾 Festplatten-Speicher"
                                                       FontSize="16"
                                                       FontWeight="SemiBold"
                                                       Foreground="#0F172A"
                                                       Margin="0,0,0,16"/>

                                            <ItemsControl ItemsSource="{Binding DiskPartitions}">
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate>
                                                        <Border Background="White"
                                                                CornerRadius="12"
                                                                Padding="16,12"
                                                                Margin="0,0,0,12"
                                                                BorderBrush="#EDE9FE"
                                                                BorderThickness="2">
                                                            <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="Auto"/>
                                                                    <ColumnDefinition Width="*"/>
                                                                    <ColumnDefinition Width="Auto"/>
                                                                </Grid.ColumnDefinitions>

                                                                <!-- Laufwerksbuchstabe -->
                                                                <Border Grid.Column="0"
                                                                        Background="#7C3AED"
                                                                        CornerRadius="10"
                                                                        Width="36"
                                                                        Height="36"
                                                                        Margin="0,0,16,0">
                                                                    <TextBlock Text="{Binding DriveLetter}"
                                                                               FontSize="16"
                                                                               FontWeight="Bold"
                                                                               Foreground="White"
                                                                               HorizontalAlignment="Center"
                                                                               VerticalAlignment="Center"/>
                                                                </Border>

                                                                <!-- Laufwerk-Info -->
                                                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                                    <TextBlock Text="{Binding Label}"
                                                                               FontSize="14"
                                                                               FontWeight="SemiBold"
                                                                               Foreground="#0F172A"
                                                                               Margin="0,0,0,4"/>
                                                                    <TextBlock Text="{Binding DisplayText}"
                                                                               FontSize="12"
                                                                               FontWeight="Medium"
                                                                               Foreground="#64748B"/>
                                                                </StackPanel>

                                                                <!-- Auslastung -->
                                                                <Border Grid.Column="2"
                                                                        Background="{Binding UsageColor}"
                                                                        CornerRadius="12"
                                                                        Padding="12,6"
                                                                        VerticalAlignment="Center">
                                                                    <TextBlock Text="{Binding UsagePercent, StringFormat={}{0:F0}%}"
                                                                               FontSize="13"
                                                                               FontWeight="Bold"
                                                                               Foreground="White"/>
                                                                </Border>
                                                            </Grid>
                                                        </Border>
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                                </Expander>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>

        <!-- Dashboard Footer -->
        <Border Grid.Row="3"
                Background="#0F172A"
                Padding="32,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Text="© 2025 Haug Components - Server-Infrastruktur Management"
                           FontSize="13"
                           FontWeight="Medium"
                           Foreground="#64748B"
                           VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Border Background="#1E293B"
                            CornerRadius="8"
                            Padding="12,6"
                            Margin="0,0,16,0">
                        <TextBlock Text="⚡ Auto-Update: 30s"
                                   FontSize="12"
                                   FontWeight="Medium"
                                   Foreground="#94A3B8"
                                   VerticalAlignment="Center"/>
                    </Border>
                    <Border Background="#1E293B"
                            CornerRadius="8"
                            Padding="12,6">
                        <TextBlock Text="🟢 System aktiv"
                                   FontSize="12"
                                   Foreground="#10B981"
                                   FontWeight="SemiBold"/>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
