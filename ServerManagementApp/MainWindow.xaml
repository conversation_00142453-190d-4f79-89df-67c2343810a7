<Window
    x:Class="ServerManagementApp.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:ServerManagementApp"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="Server Management App" Height="600" Width="900"
    WindowStartupLocation="CenterScreen">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title Bar -->
        <Border Grid.Row="0" Background="#FF0078D4" Padding="16,8">
            <TextBlock Text="Server Management App"
                       FontSize="20"
                       FontWeight="SemiBold"
                       Foreground="White"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" Padding="20">
            <StackPanel>
                <TextBlock Text="Willkommen zur Server Management App!"
                           FontSize="24"
                           FontWeight="Bold"
                           HorizontalAlignment="Center"
                           Margin="0,20,0,10"/>

                <TextBlock Text="Diese WPF Anwendung hilft Ihnen bei der Verwaltung Ihrer Server."
                           FontSize="16"
                           TextWrapping="Wrap"
                           HorizontalAlignment="Center"
                           Foreground="Gray"
                           Margin="0,0,0,30"/>

                <!-- Server Status Cards -->
                <Grid Margin="0,30,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Server 1 -->
                    <Border Grid.Column="0"
                            Background="LightGray"
                            CornerRadius="8"
                            Padding="16"
                            Margin="0,0,10,0">
                        <StackPanel>
                            <TextBlock Text="Web Server" FontWeight="SemiBold" FontSize="16"/>
                            <TextBlock Text="Status: Online" Foreground="Green" Margin="0,5,0,0"/>
                            <TextBlock Text="CPU: 45%" Margin="0,2,0,0"/>
                            <TextBlock Text="RAM: 2.1GB / 8GB" Margin="0,2,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Server 2 -->
                    <Border Grid.Column="1"
                            Background="LightGray"
                            CornerRadius="8"
                            Padding="16"
                            Margin="5,0">
                        <StackPanel>
                            <TextBlock Text="Database Server" FontWeight="SemiBold" FontSize="16"/>
                            <TextBlock Text="Status: Online" Foreground="Green" Margin="0,5,0,0"/>
                            <TextBlock Text="CPU: 23%" Margin="0,2,0,0"/>
                            <TextBlock Text="RAM: 4.7GB / 16GB" Margin="0,2,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Server 3 -->
                    <Border Grid.Column="2"
                            Background="LightGray"
                            CornerRadius="8"
                            Padding="16"
                            Margin="10,0,0,0">
                        <StackPanel>
                            <TextBlock Text="File Server" FontWeight="SemiBold" FontSize="16"/>
                            <TextBlock Text="Status: Wartung" Foreground="Orange" Margin="0,5,0,0"/>
                            <TextBlock Text="CPU: 12%" Margin="0,2,0,0"/>
                            <TextBlock Text="RAM: 1.2GB / 4GB" Margin="0,2,0,0"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            Margin="0,30,0,0">
                    <Button Content="Server hinzufügen"
                            Background="#FF0078D4"
                            Foreground="White"
                            Padding="15,8"
                            Margin="0,0,15,0"
                            Click="AddServerButton_Click"/>
                    <Button Content="Alle Server aktualisieren"
                            Padding="15,8"
                            Margin="0,0,15,0"
                            Click="RefreshAllButton_Click"/>
                    <Button Content="Einstellungen"
                            Padding="15,8"
                            Click="SettingsButton_Click"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2"
                Background="LightGray"
                Padding="16,8">
            <Grid>
                <TextBlock Text="Bereit"
                           VerticalAlignment="Center"/>
                <TextBlock x:Name="TimeTextBlock"
                           Text="11:30:00"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           Foreground="Gray"/>
            </Grid>
        </Border>
    </Grid>
</Window>
