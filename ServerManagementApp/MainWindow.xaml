<Window
    x:Class="ServerManagementApp.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:ServerManagementApp"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title Bar -->
        <Border Grid.Row="0" Background="{ThemeResource SystemControlBackgroundAccentBrush}" Padding="16,8">
            <TextBlock Text="Server Management App" 
                       FontSize="20" 
                       FontWeight="SemiBold" 
                       Foreground="White"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" Padding="20">
            <StackPanel Spacing="20">
                <TextBlock Text="Willkommen zur Server Management App!" 
                           FontSize="24" 
                           FontWeight="Bold"
                           HorizontalAlignment="Center"
                           Margin="0,20,0,10"/>
                
                <TextBlock Text="Diese WinUI 3 Anwendung hilft Ihnen bei der Verwaltung Ihrer Server." 
                           FontSize="16"
                           TextWrapping="Wrap"
                           HorizontalAlignment="Center"
                           Foreground="{ThemeResource SystemControlForegroundBaseMediumBrush}"/>

                <!-- Server Status Cards -->
                <Grid Margin="0,30,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Server 1 -->
                    <Border Grid.Column="0" 
                            Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                            CornerRadius="8"
                            Padding="16"
                            Margin="0,0,10,0">
                        <StackPanel>
                            <TextBlock Text="Web Server" FontWeight="SemiBold" FontSize="16"/>
                            <TextBlock Text="Status: Online" Foreground="Green" Margin="0,5,0,0"/>
                            <TextBlock Text="CPU: 45%" Margin="0,2,0,0"/>
                            <TextBlock Text="RAM: 2.1GB / 8GB" Margin="0,2,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Server 2 -->
                    <Border Grid.Column="1" 
                            Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                            CornerRadius="8"
                            Padding="16"
                            Margin="5,0">
                        <StackPanel>
                            <TextBlock Text="Database Server" FontWeight="SemiBold" FontSize="16"/>
                            <TextBlock Text="Status: Online" Foreground="Green" Margin="0,5,0,0"/>
                            <TextBlock Text="CPU: 23%" Margin="0,2,0,0"/>
                            <TextBlock Text="RAM: 4.7GB / 16GB" Margin="0,2,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Server 3 -->
                    <Border Grid.Column="2" 
                            Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                            CornerRadius="8"
                            Padding="16"
                            Margin="10,0,0,0">
                        <StackPanel>
                            <TextBlock Text="File Server" FontWeight="SemiBold" FontSize="16"/>
                            <TextBlock Text="Status: Wartung" Foreground="Orange" Margin="0,5,0,0"/>
                            <TextBlock Text="CPU: 12%" Margin="0,2,0,0"/>
                            <TextBlock Text="RAM: 1.2GB / 4GB" Margin="0,2,0,0"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center" 
                            Spacing="15"
                            Margin="0,30,0,0">
                    <Button Content="Server hinzufügen" 
                            Style="{StaticResource AccentButtonStyle}"
                            Click="AddServerButton_Click"/>
                    <Button Content="Alle Server aktualisieren" 
                            Click="RefreshAllButton_Click"/>
                    <Button Content="Einstellungen" 
                            Click="SettingsButton_Click"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2" 
                Background="{ThemeResource SystemControlBackgroundBaseLowBrush}" 
                Padding="16,8">
            <Grid>
                <TextBlock Text="Bereit" 
                           VerticalAlignment="Center"/>
                <TextBlock Text="11:30:00"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           Foreground="{ThemeResource SystemControlForegroundBaseMediumBrush}"/>
            </Grid>
        </Border>
    </Grid>
</Window>
