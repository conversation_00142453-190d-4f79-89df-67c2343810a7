<Window
    x:Class="ServerManagementApp.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:ServerManagementApp"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="Server Management App" Height="900" Width="1600"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    UseLayoutRounding="True"
    TextOptions.TextFormattingMode="Display"
    TextOptions.TextRenderingMode="ClearType"
    RenderOptions.BitmapScalingMode="HighQuality">

    <Window.Resources>
        <local:PercentToWidthConverter x:Key="PercentToWidthConverter"/>
        <local:PercentToColorConverter x:Key="PercentToColorConverter"/>
    </Window.Resources>

    <Grid Background="#F8FAFC">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Dashboard Header -->
        <Border Grid.Row="0" Background="#1E293B" Padding="32,24">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="Haug Components"
                               FontSize="32"
                               FontWeight="ExtraLight"
                               Foreground="White"
                               Margin="0,0,0,6"/>
                    <TextBlock Text="Server-Infrastruktur Dashboard"
                               FontSize="18"
                               FontWeight="Light"
                               Foreground="#CBD5E1"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Border Background="#0F172A"
                            CornerRadius="8"
                            Padding="16,12"
                            Margin="0,0,16,0">
                        <StackPanel>
                            <TextBlock Text="Letzte Aktualisierung"
                                       FontSize="11"
                                       FontWeight="Medium"
                                       Foreground="#64748B"
                                       Margin="0,0,0,4"/>
                            <TextBlock x:Name="LastUpdateTextBlock"
                                       Text="Gerade eben"
                                       FontSize="15"
                                       FontWeight="SemiBold"
                                       Foreground="White"/>
                        </StackPanel>
                    </Border>

                    <Border Background="#0F172A"
                            CornerRadius="8"
                            Padding="16,12">
                        <StackPanel>
                            <TextBlock Text="Aktuelle Zeit"
                                       FontSize="11"
                                       FontWeight="Medium"
                                       Foreground="#64748B"
                                       Margin="0,0,0,4"/>
                            <TextBlock x:Name="TimeTextBlock"
                                       Text="11:30:00"
                                       FontSize="15"
                                       FontWeight="SemiBold"
                                       Foreground="White"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Dashboard Statistics -->
        <Border Grid.Row="1" Background="White" Padding="32,24" BorderBrush="#E2E8F0" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Gesamt Server -->
                <Border Grid.Column="0" Background="White" CornerRadius="16" Padding="24,20" Margin="0,0,16,0" BorderBrush="#E2E8F0" BorderThickness="1">
                    <StackPanel>
                        <Border Background="#F1F5F9" CornerRadius="12" Width="48" Height="48" Margin="0,0,0,12">
                            <TextBlock Text="🖥️" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="TotalServersText" Text="7" FontSize="36" FontWeight="ExtraLight" Foreground="#0F172A" Margin="0,0,0,4"/>
                        <TextBlock Text="Gesamt Server" FontSize="13" FontWeight="Medium" Foreground="#64748B"/>
                    </StackPanel>
                </Border>

                <!-- Online Server -->
                <Border Grid.Column="1" Background="White" CornerRadius="16" Padding="24,20" Margin="8,0" BorderBrush="#10B981" BorderThickness="2">
                    <StackPanel>
                        <Border Background="#DCFCE7" CornerRadius="12" Width="48" Height="48" Margin="0,0,0,12">
                            <TextBlock Text="✅" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="OnlineServersText" Text="5" FontSize="36" FontWeight="ExtraLight" Foreground="#059669" Margin="0,0,0,4"/>
                        <TextBlock Text="Online" FontSize="13" FontWeight="Medium" Foreground="#10B981"/>
                    </StackPanel>
                </Border>

                <!-- Offline Server -->
                <Border Grid.Column="2" Background="White" CornerRadius="16" Padding="24,20" Margin="8,0" BorderBrush="#EF4444" BorderThickness="2">
                    <StackPanel>
                        <Border Background="#FEE2E2" CornerRadius="12" Width="48" Height="48" Margin="0,0,0,12">
                            <TextBlock Text="⚠️" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="OfflineServersText" Text="2" FontSize="36" FontWeight="ExtraLight" Foreground="#DC2626" Margin="0,0,0,4"/>
                        <TextBlock Text="Offline" FontSize="13" FontWeight="Medium" Foreground="#EF4444"/>
                    </StackPanel>
                </Border>

                <!-- Durchschnittliche Antwortzeit -->
                <Border Grid.Column="3" Background="White" CornerRadius="16" Padding="24,20" Margin="8,0,16,0" BorderBrush="#F59E0B" BorderThickness="2">
                    <StackPanel>
                        <Border Background="#FEF3C7" CornerRadius="12" Width="48" Height="48" Margin="0,0,0,12">
                            <TextBlock Text="⚡" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="AvgResponseText" Text="12ms" FontSize="36" FontWeight="ExtraLight" Foreground="#D97706" Margin="0,0,0,4"/>
                        <TextBlock Text="Ø Antwortzeit" FontSize="13" FontWeight="Medium" Foreground="#F59E0B"/>
                    </StackPanel>
                </Border>

                <!-- Aktions-Buttons -->
                <StackPanel Grid.Column="4" Orientation="Horizontal">
                    <Border Background="#3B82F6" CornerRadius="12" Margin="0,0,16,0">
                        <Button Content="🔄 Alle aktualisieren"
                                Background="Transparent"
                                Foreground="White"
                                Padding="24,16"
                                FontWeight="Medium"
                                FontSize="14"
                                BorderThickness="0"
                                Click="RefreshAllButton_Click"/>
                    </Border>
                    <Border Background="#10B981" CornerRadius="12">
                        <Button Content="➕ Server hinzufügen"
                                Background="Transparent"
                                Foreground="White"
                                Padding="24,16"
                                FontWeight="Medium"
                                FontSize="14"
                                BorderThickness="0"
                                Click="AddServerButton_Click"/>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Server Grid -->
        <ScrollViewer Grid.Row="2" Padding="32,24">
            <StackPanel>
                <TextBlock Text="Server-Übersicht"
                           FontSize="28"
                           FontWeight="Light"
                           Foreground="#0F172A"
                           Margin="0,0,0,32"/>

                <!-- Server Grid -->
                <ItemsControl x:Name="ServerList">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <UniformGrid Columns="3" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="White"
                                    CornerRadius="20"
                                    Padding="0"
                                    Margin="16"
                                    MinHeight="360"
                                    BorderBrush="#F1F5F9"
                                    BorderThickness="1"
                                    UseLayoutRounding="True"
                                    SnapsToDevicePixels="True">
                                <Border.Effect>
                                    <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="4" Opacity="0.08" BlurRadius="20"/>
                                </Border.Effect>

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Server Header -->
                                    <Border Grid.Row="0"
                                            Background="#FAFBFC"
                                            CornerRadius="20,20,0,0"
                                            Padding="28,24"
                                            UseLayoutRounding="True">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Server Icon -->
                                            <Border Grid.Column="0"
                                                    Background="#6366F1"
                                                    CornerRadius="16"
                                                    Width="56"
                                                    Height="56"
                                                    Margin="0,0,20,0">
                                                <TextBlock Text="🖥️"
                                                           FontSize="28"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                            </Border>

                                            <!-- Server Info -->
                                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding Name}"
                                                           FontWeight="SemiBold"
                                                           FontSize="22"
                                                           Foreground="#0F172A"
                                                           Margin="0,0,0,6"/>
                                                <TextBlock Text="{Binding Description}"
                                                           FontSize="15"
                                                           FontWeight="Medium"
                                                           Foreground="#64748B"/>
                                            </StackPanel>

                                            <!-- Status Badge -->
                                            <Border Grid.Column="2"
                                                    CornerRadius="12"
                                                    Padding="16,8"
                                                    Background="{Binding StatusColor}"
                                                    VerticalAlignment="Top"
                                                    UseLayoutRounding="True">
                                                <TextBlock Text="{Binding StatusText}"
                                                           Foreground="White"
                                                           FontWeight="SemiBold"
                                                           FontSize="13"/>
                                            </Border>
                                        </Grid>
                                    </Border>

                                    <!-- Content Area -->
                                    <StackPanel Grid.Row="1" Margin="28,24">

                                        <!-- Netzwerk & Verbindungsinfo -->
                                        <Border Background="#F8FAFC" CornerRadius="16" Padding="20,16" Margin="0,0,0,20">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <Border Grid.Column="0"
                                                        Background="#DBEAFE"
                                                        CornerRadius="10"
                                                        Width="36"
                                                        Height="36"
                                                        Margin="0,0,16,0">
                                                    <TextBlock Text="🌐"
                                                               FontSize="18"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                                </Border>

                                                <TextBlock Grid.Column="1"
                                                           Text="{Binding IpAddress}"
                                                           FontSize="17"
                                                           FontWeight="SemiBold"
                                                           Foreground="#0F172A"
                                                           VerticalAlignment="Center"/>

                                                <TextBlock Grid.Column="2"
                                                           Text="{Binding PingTimeText}"
                                                           FontSize="13"
                                                           FontWeight="Medium"
                                                           Foreground="#64748B"
                                                           VerticalAlignment="Center"/>
                                            </Grid>
                                        </Border>

                                        <!-- System-Ressourcen -->
                                        <Grid Margin="0,0,0,20">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- CPU-Auslastung -->
                                            <Border Grid.Column="0"
                                                    Background="#FEF3C7"
                                                    CornerRadius="16"
                                                    Padding="20,16"
                                                    Margin="0,0,10,0"
                                                    UseLayoutRounding="True">
                                                <StackPanel>
                                                    <Grid Margin="0,0,0,12">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                        </Grid.ColumnDefinitions>
                                                        <Border Grid.Column="0" Background="#FDE68A" CornerRadius="8" Width="32" Height="32" Margin="0,0,12,0">
                                                            <TextBlock Text="💻" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                        <TextBlock Grid.Column="1" Text="CPU-Auslastung"
                                                                   FontSize="13"
                                                                   Foreground="#D97706"
                                                                   FontWeight="SemiBold"
                                                                   VerticalAlignment="Center"/>
                                                    </Grid>
                                                    <TextBlock Text="{Binding CpuUsage, StringFormat={}{0}%}"
                                                               FontSize="28"
                                                               FontWeight="Light"
                                                               Foreground="#92400E"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- Speicher-Auslastung -->
                                            <Border Grid.Column="1"
                                                    Background="#D1FAE5"
                                                    CornerRadius="16"
                                                    Padding="20,16"
                                                    Margin="10,0,0,0"
                                                    UseLayoutRounding="True">
                                                <StackPanel>
                                                    <Grid Margin="0,0,0,12">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                        </Grid.ColumnDefinitions>
                                                        <Border Grid.Column="0" Background="#A7F3D0" CornerRadius="8" Width="32" Height="32" Margin="0,0,12,0">
                                                            <TextBlock Text="🧠" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                        <TextBlock Grid.Column="1" Text="Arbeitsspeicher"
                                                                   FontSize="13"
                                                                   Foreground="#059669"
                                                                   FontWeight="SemiBold"
                                                                   VerticalAlignment="Center"/>
                                                    </Grid>
                                                    <TextBlock Text="{Binding MemoryUsageText}"
                                                               FontSize="15"
                                                               FontWeight="Medium"
                                                               Foreground="#047857"
                                                               TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </Border>
                                        </Grid>

                                        <!-- Festplatten-Speicher -->
                                        <Border Background="#EDE9FE"
                                                CornerRadius="16"
                                                Padding="20,16"
                                                UseLayoutRounding="True">
                                            <StackPanel>
                                                <Grid Margin="0,0,0,16">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Border Grid.Column="0" Background="#C4B5FD" CornerRadius="8" Width="32" Height="32" Margin="0,0,12,0">
                                                        <TextBlock Text="💾" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                    <TextBlock Grid.Column="1" Text="Speicher-Übersicht"
                                                               FontSize="13"
                                                               Foreground="#7C3AED"
                                                               FontWeight="SemiBold"
                                                               VerticalAlignment="Center"/>
                                                </Grid>

                                                <ItemsControl ItemsSource="{Binding DiskPartitions}">
                                                    <ItemsControl.ItemTemplate>
                                                        <DataTemplate>
                                                            <Border Background="White"
                                                                    CornerRadius="12"
                                                                    Padding="16,12"
                                                                    Margin="0,0,0,8"
                                                                    BorderBrush="#F1F5F9"
                                                                    BorderThickness="1">
                                                                <Grid>
                                                                    <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                    </Grid.ColumnDefinitions>

                                                                    <!-- Laufwerksbuchstabe -->
                                                                    <Border Grid.Column="0"
                                                                            Background="#7C3AED"
                                                                            CornerRadius="10"
                                                                            Width="36"
                                                                            Height="36"
                                                                            Margin="0,0,16,0">
                                                                        <TextBlock Text="{Binding DriveLetter}"
                                                                                   FontSize="16"
                                                                                   FontWeight="SemiBold"
                                                                                   Foreground="White"
                                                                                   HorizontalAlignment="Center"
                                                                                   VerticalAlignment="Center"/>
                                                                    </Border>

                                                                    <!-- Laufwerk-Info -->
                                                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                                        <TextBlock Text="{Binding Label}"
                                                                                   FontSize="14"
                                                                                   FontWeight="SemiBold"
                                                                                   Foreground="#0F172A"
                                                                                   Margin="0,0,0,4"/>
                                                                        <TextBlock Text="{Binding DisplayText}"
                                                                                   FontSize="12"
                                                                                   FontWeight="Medium"
                                                                                   Foreground="#64748B"/>
                                                                    </StackPanel>

                                                                    <!-- Auslastung in Prozent -->
                                                                    <Border Grid.Column="2"
                                                                            Background="{Binding UsageColor}"
                                                                            CornerRadius="16"
                                                                            Padding="12,6"
                                                                            VerticalAlignment="Center">
                                                                        <TextBlock Text="{Binding UsagePercent, StringFormat={}{0:F0}%}"
                                                                                   FontSize="12"
                                                                                   FontWeight="Bold"
                                                                                   Foreground="White"/>
                                                                    </Border>
                                                                </Grid>
                                                            </Border>
                                                        </DataTemplate>
                                                    </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>

                                    <!-- Footer -->
                                    <Border Grid.Row="2"
                                            Background="#F8FAFC"
                                            CornerRadius="0,0,16,16"
                                            Padding="24,12"
                                            UseLayoutRounding="True">
                                        <TextBlock Text="{Binding LastPingText}"
                                                   FontSize="11"
                                                   Foreground="#94A3B8"
                                                   HorizontalAlignment="Center"/>
                                    </Border>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>

        <!-- Dashboard Footer -->
        <Border Grid.Row="3"
                Background="#0F172A"
                Padding="32,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Text="© 2025 Haug Components - Server-Infrastruktur Management"
                           FontSize="13"
                           FontWeight="Medium"
                           Foreground="#64748B"
                           VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Border Background="#1E293B"
                            CornerRadius="8"
                            Padding="12,6"
                            Margin="0,0,16,0">
                        <TextBlock Text="⚡ Auto-Update: 30s"
                                   FontSize="12"
                                   FontWeight="Medium"
                                   Foreground="#94A3B8"
                                   VerticalAlignment="Center"/>
                    </Border>
                    <Border Background="#1E293B"
                            CornerRadius="8"
                            Padding="12,6">
                        <TextBlock Text="🟢 System aktiv"
                                   FontSize="12"
                                   Foreground="#10B981"
                                   FontWeight="SemiBold"/>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
