<Window
    x:Class="ServerManagementApp.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:ServerManagementApp"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="Server Management App" Height="700" Width="1200"
    WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <local:PercentToWidthConverter x:Key="PercentToWidthConverter"/>
        <local:PercentToColorConverter x:Key="PercentToColorConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title Bar -->
        <Border Grid.Row="0" Background="#FF0078D4" Padding="16,8">
            <TextBlock Text="Server Management App"
                       FontSize="20"
                       FontWeight="SemiBold"
                       Foreground="White"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" Padding="20">
            <StackPanel>
                <TextBlock Text="Server Management Dashboard"
                           FontSize="24"
                           FontWeight="Bold"
                           HorizontalAlignment="Center"
                           Margin="0,20,0,10"/>

                <TextBlock Text="Überwachung der Haug Components Server-Infrastruktur"
                           FontSize="16"
                           TextWrapping="Wrap"
                           HorizontalAlignment="Center"
                           Foreground="Gray"
                           Margin="0,0,0,20"/>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            Margin="0,0,0,20">
                    <Button Content="Alle Server pingen"
                            Background="#FF0078D4"
                            Foreground="White"
                            Padding="15,8"
                            Margin="0,0,15,0"
                            Click="RefreshAllButton_Click"/>
                    <Button Content="Server hinzufügen"
                            Padding="15,8"
                            Margin="0,0,15,0"
                            Click="AddServerButton_Click"/>
                    <Button Content="Einstellungen"
                            Padding="15,8"
                            Click="SettingsButton_Click"/>
                </StackPanel>

                <!-- Server List -->
                <ItemsControl x:Name="ServerList" Margin="0,20,0,0">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <UniformGrid Columns="3" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="LightGray"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="5"
                                    MinHeight="200">
                                <StackPanel>
                                    <TextBlock Text="{Binding Name}"
                                               FontWeight="SemiBold"
                                               FontSize="16"
                                               Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding Description}"
                                               FontSize="12"
                                               Foreground="Gray"
                                               Margin="0,0,0,8"/>
                                    <TextBlock Text="{Binding IpAddress}"
                                               FontSize="11"
                                               Foreground="DarkGray"
                                               Margin="0,0,0,8"/>
                                    <TextBlock Text="{Binding StatusText}"
                                               Foreground="{Binding StatusColor}"
                                               FontWeight="SemiBold"
                                               Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding CpuUsageText}"
                                               Margin="0,2,0,0"/>
                                    <TextBlock Text="{Binding MemoryUsageText}"
                                               Margin="0,2,0,0"/>
                                    <TextBlock Text="💾 Festplatten:"
                                               FontSize="11"
                                               Margin="0,8,0,4"/>
                                    <TextBlock Text="C: 65% (65/100 GB)"
                                               FontSize="10"
                                               Margin="0,1"/>
                                    <TextBlock Text="D: 45% (90/200 GB)"
                                               FontSize="10"
                                               Margin="0,1"/>
                                    <TextBlock Text="{Binding PingTimeText}"
                                               FontSize="11"
                                               Foreground="Gray"
                                               Margin="0,8,0,0"/>
                                </StackPanel>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2"
                Background="LightGray"
                Padding="16,8">
            <Grid>
                <TextBlock Text="Bereit"
                           VerticalAlignment="Center"/>
                <TextBlock x:Name="TimeTextBlock"
                           Text="11:30:00"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           Foreground="Gray"/>
            </Grid>
        </Border>
    </Grid>
</Window>
