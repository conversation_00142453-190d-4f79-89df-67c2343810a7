<Window
    x:Class="ServerManagementApp.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:ServerManagementApp"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="Server Management App" Height="700" Width="1200"
    WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <local:PercentToWidthConverter x:Key="PercentToWidthConverter"/>
        <local:PercentToColorConverter x:Key="PercentToColorConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title Bar -->
        <Border Grid.Row="0" Background="#FF0078D4" Padding="16,8">
            <TextBlock Text="Server Management App"
                       FontSize="20"
                       FontWeight="SemiBold"
                       Foreground="White"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" Padding="20">
            <StackPanel>
                <TextBlock Text="Server Management Dashboard"
                           FontSize="24"
                           FontWeight="Bold"
                           HorizontalAlignment="Center"
                           Margin="0,20,0,10"/>

                <TextBlock Text="Überwachung der Haug Components Server-Infrastruktur"
                           FontSize="16"
                           TextWrapping="Wrap"
                           HorizontalAlignment="Center"
                           Foreground="Gray"
                           Margin="0,0,0,20"/>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            Margin="0,0,0,20">
                    <Button Content="Alle Server pingen"
                            Background="#FF0078D4"
                            Foreground="White"
                            Padding="15,8"
                            Margin="0,0,15,0"
                            Click="RefreshAllButton_Click"/>
                    <Button Content="Server hinzufügen"
                            Padding="15,8"
                            Margin="0,0,15,0"
                            Click="AddServerButton_Click"/>
                    <Button Content="Einstellungen"
                            Padding="15,8"
                            Click="SettingsButton_Click"/>
                </StackPanel>

                <!-- Server List -->
                <ItemsControl x:Name="ServerList" Margin="0,20,0,0">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <UniformGrid Columns="2" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="White"
                                    CornerRadius="12"
                                    Padding="0"
                                    Margin="12"
                                    MinHeight="240"
                                    BorderBrush="#E0E0E0"
                                    BorderThickness="1">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                                </Border.Effect>

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header mit Status -->
                                    <Border Grid.Row="0"
                                            Background="#F8F9FA"
                                            CornerRadius="12,12,0,0"
                                            Padding="20,16">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="{Binding Name}"
                                                           FontWeight="Bold"
                                                           FontSize="18"
                                                           Foreground="#2C3E50"
                                                           Margin="0,0,0,4"/>
                                                <TextBlock Text="{Binding Description}"
                                                           FontSize="13"
                                                           Foreground="#6C757D"/>
                                            </StackPanel>

                                            <Border Grid.Column="1"
                                                    CornerRadius="20"
                                                    Padding="12,6"
                                                    Background="{Binding StatusColor}"
                                                    VerticalAlignment="Center">
                                                <TextBlock Text="{Binding StatusText}"
                                                           Foreground="White"
                                                           FontWeight="SemiBold"
                                                           FontSize="11"/>
                                            </Border>
                                        </Grid>
                                    </Border>

                                    <!-- Content Area -->
                                    <StackPanel Grid.Row="1" Margin="20,16">

                                        <!-- Network Info -->
                                        <Grid Margin="0,0,0,16">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <Border Grid.Column="0"
                                                    Background="#E3F2FD"
                                                    CornerRadius="6"
                                                    Padding="8,4"
                                                    Margin="0,0,12,0">
                                                <TextBlock Text="🌐" FontSize="14"/>
                                            </Border>

                                            <TextBlock Grid.Column="1"
                                                       Text="{Binding IpAddress}"
                                                       FontSize="14"
                                                       FontWeight="Medium"
                                                       Foreground="#495057"
                                                       VerticalAlignment="Center"/>

                                            <TextBlock Grid.Column="2"
                                                       Text="{Binding PingTimeText}"
                                                       FontSize="12"
                                                       Foreground="#6C757D"
                                                       VerticalAlignment="Center"/>
                                        </Grid>

                                        <!-- System Resources -->
                                        <Grid Margin="0,0,0,16">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- CPU -->
                                            <Border Grid.Column="0"
                                                    Background="#FFF3E0"
                                                    CornerRadius="8"
                                                    Padding="12,8"
                                                    Margin="0,0,6,0">
                                                <StackPanel>
                                                    <TextBlock Text="CPU"
                                                               FontSize="11"
                                                               Foreground="#FF8F00"
                                                               FontWeight="SemiBold"
                                                               Margin="0,0,0,4"/>
                                                    <TextBlock Text="{Binding CpuUsage, StringFormat={}{0}%}"
                                                               FontSize="18"
                                                               FontWeight="Bold"
                                                               Foreground="#E65100"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- Memory -->
                                            <Border Grid.Column="1"
                                                    Background="#E8F5E8"
                                                    CornerRadius="8"
                                                    Padding="12,8"
                                                    Margin="6,0,0,0">
                                                <StackPanel>
                                                    <TextBlock Text="RAM"
                                                               FontSize="11"
                                                               Foreground="#2E7D32"
                                                               FontWeight="SemiBold"
                                                               Margin="0,0,0,4"/>
                                                    <TextBlock Text="{Binding MemoryUsageText}"
                                                               FontSize="11"
                                                               FontWeight="SemiBold"
                                                               Foreground="#1B5E20"
                                                               TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </Border>
                                        </Grid>

                                        <!-- Disk Info -->
                                        <Border Background="#F3E5F5"
                                                CornerRadius="8"
                                                Padding="12,8">
                                            <StackPanel>
                                                <TextBlock Text="💾 Festplatten"
                                                           FontSize="11"
                                                           Foreground="#7B1FA2"
                                                           FontWeight="SemiBold"
                                                           Margin="0,0,0,6"/>
                                                <TextBlock Text="C: 65% (65/100 GB)"
                                                           FontSize="11"
                                                           Foreground="#4A148C"
                                                           Margin="0,1"/>
                                                <TextBlock Text="D: 45% (90/200 GB)"
                                                           FontSize="11"
                                                           Foreground="#4A148C"
                                                           Margin="0,1"/>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>

                                    <!-- Footer -->
                                    <Border Grid.Row="2"
                                            Background="#F8F9FA"
                                            CornerRadius="0,0,12,12"
                                            Padding="20,12">
                                        <TextBlock Text="{Binding LastPingText}"
                                                   FontSize="10"
                                                   Foreground="#ADB5BD"
                                                   HorizontalAlignment="Right"/>
                                    </Border>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2"
                Background="LightGray"
                Padding="16,8">
            <Grid>
                <TextBlock Text="Bereit"
                           VerticalAlignment="Center"/>
                <TextBlock x:Name="TimeTextBlock"
                           Text="11:30:00"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           Foreground="Gray"/>
            </Grid>
        </Border>
    </Grid>
</Window>
