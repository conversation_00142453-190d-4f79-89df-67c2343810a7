<Window
    x:Class="ServerManagementApp.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:ServerManagementApp"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="Server Management App" Height="700" Width="1200"
    WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <local:PercentToWidthConverter x:Key="PercentToWidthConverter"/>
        <local:PercentToColorConverter x:Key="PercentToColorConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title Bar -->
        <Border Grid.Row="0" Background="#FF0078D4" Padding="16,8">
            <TextBlock Text="Server Management App"
                       FontSize="20"
                       FontWeight="SemiBold"
                       Foreground="White"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" Padding="20">
            <StackPanel>
                <TextBlock Text="Server Management Dashboard"
                           FontSize="24"
                           FontWeight="Bold"
                           HorizontalAlignment="Center"
                           Margin="0,20,0,10"/>

                <TextBlock Text="Überwachung der Haug Components Server-Infrastruktur"
                           FontSize="16"
                           TextWrapping="Wrap"
                           HorizontalAlignment="Center"
                           Foreground="Gray"
                           Margin="0,0,0,20"/>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            Margin="0,0,0,20">
                    <Button Content="Alle Server pingen"
                            Background="#FF0078D4"
                            Foreground="White"
                            Padding="15,8"
                            Margin="0,0,15,0"
                            Click="RefreshAllButton_Click"/>
                    <Button Content="Server hinzufügen"
                            Padding="15,8"
                            Margin="0,0,15,0"
                            Click="AddServerButton_Click"/>
                    <Button Content="Einstellungen"
                            Padding="15,8"
                            Click="SettingsButton_Click"/>
                </StackPanel>

                <!-- Server List -->
                <ItemsControl x:Name="ServerList" Margin="0,20,0,0">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <UniformGrid Columns="2" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="White"
                                    CornerRadius="12"
                                    Padding="20"
                                    Margin="8"
                                    MinHeight="280"
                                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Setter Property="BorderBrush" Value="LightGray"/>
                                        <Setter Property="BorderThickness" Value="1"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Status}" Value="Online">
                                                <Setter Property="BorderBrush" Value="#4CAF50"/>
                                                <Setter Property="BorderThickness" Value="2"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Status}" Value="Offline">
                                                <Setter Property="BorderBrush" Value="#F44336"/>
                                                <Setter Property="BorderThickness" Value="2"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Status}" Value="Warning">
                                                <Setter Property="BorderBrush" Value="#FF9800"/>
                                                <Setter Property="BorderThickness" Value="2"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header -->
                                    <Grid Grid.Row="0" Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="{Binding Name}"
                                                       FontWeight="Bold"
                                                       FontSize="18"
                                                       Foreground="#2C3E50"/>
                                            <TextBlock Text="{Binding Description}"
                                                       FontSize="12"
                                                       Foreground="#7F8C8D"
                                                       Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <Border Grid.Column="1"
                                                CornerRadius="12"
                                                Padding="8,4"
                                                Background="{Binding StatusColor}">
                                            <TextBlock Text="{Binding StatusText}"
                                                       Foreground="White"
                                                       FontWeight="SemiBold"
                                                       FontSize="11"/>
                                        </Border>
                                    </Grid>

                                    <!-- Network Info -->
                                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,12">
                                        <TextBlock Text="🌐" FontSize="14" Margin="0,0,6,0"/>
                                        <TextBlock Text="{Binding IpAddress}"
                                                   FontSize="12"
                                                   Foreground="#34495E"
                                                   VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding PingTimeText}"
                                                   FontSize="11"
                                                   Foreground="#7F8C8D"
                                                   Margin="12,0,0,0"
                                                   VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- System Resources -->
                                    <Grid Grid.Row="2" Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- CPU -->
                                        <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                            <TextBlock Text="💻 CPU" FontSize="11" Foreground="#7F8C8D" Margin="0,0,0,4"/>
                                            <TextBlock Text="{Binding CpuUsage, StringFormat={}{0}%}"
                                                       FontSize="16"
                                                       FontWeight="SemiBold"
                                                       Foreground="#E74C3C"/>
                                        </StackPanel>

                                        <!-- Memory -->
                                        <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                            <TextBlock Text="🧠 RAM" FontSize="11" Foreground="#7F8C8D" Margin="0,0,0,4"/>
                                            <TextBlock Text="{Binding MemoryUsageText}"
                                                       FontSize="12"
                                                       FontWeight="SemiBold"
                                                       Foreground="#3498DB"/>
                                        </StackPanel>
                                    </Grid>

                                    <!-- Disk Partitions -->
                                    <StackPanel Grid.Row="3">
                                        <TextBlock Text="💾 Festplatten"
                                                   FontSize="11"
                                                   Foreground="#7F8C8D"
                                                   Margin="0,0,0,8"/>
                                        <ItemsControl ItemsSource="{Binding DiskPartitions}">
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Grid Margin="0,0,0,6">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <TextBlock Grid.Column="0"
                                                                   Text="{Binding DriveLetter}"
                                                                   FontWeight="SemiBold"
                                                                   FontSize="11"
                                                                   Width="20"
                                                                   Foreground="#2C3E50"/>

                                                        <ProgressBar Grid.Column="1"
                                                                     Value="{Binding UsagePercent}"
                                                                     Maximum="100"
                                                                     Height="16"
                                                                     Margin="4,0"
                                                                     Background="#ECF0F1">
                                                            <ProgressBar.Style>
                                                                <Style TargetType="ProgressBar">
                                                                    <Setter Property="Foreground" Value="Green"/>
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding UsagePercent, Converter={StaticResource PercentToColorConverter}}" Value="Orange">
                                                                            <Setter Property="Foreground" Value="Orange"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding UsagePercent, Converter={StaticResource PercentToColorConverter}}" Value="Red">
                                                                            <Setter Property="Foreground" Value="Red"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </ProgressBar.Style>
                                                        </ProgressBar>

                                                        <TextBlock Grid.Column="2"
                                                                   Text="{Binding UsagePercent, StringFormat={}{0:F1}%}"
                                                                   FontSize="10"
                                                                   Foreground="#7F8C8D"
                                                                   VerticalAlignment="Center"/>
                                                    </Grid>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </StackPanel>

                                    <!-- Footer -->
                                    <TextBlock Grid.Row="4"
                                               Text="{Binding LastPingText}"
                                               FontSize="10"
                                               Foreground="#BDC3C7"
                                               HorizontalAlignment="Right"
                                               Margin="0,8,0,0"/>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2"
                Background="LightGray"
                Padding="16,8">
            <Grid>
                <TextBlock Text="Bereit"
                           VerticalAlignment="Center"/>
                <TextBlock x:Name="TimeTextBlock"
                           Text="11:30:00"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           Foreground="Gray"/>
            </Grid>
        </Border>
    </Grid>
</Window>
