<Window
    x:Class="ServerManagementApp.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:ServerManagementApp"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="Server Management App" Height="900" Width="1600"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    UseLayoutRounding="True"
    TextOptions.TextFormattingMode="Display"
    TextOptions.TextRenderingMode="ClearType"
    RenderOptions.BitmapScalingMode="HighQuality">

    <Window.Resources>
        <local:PercentToWidthConverter x:Key="PercentToWidthConverter"/>
        <local:PercentToColorConverter x:Key="PercentToColorConverter"/>
    </Window.Resources>

    <Grid Background="#0D1117">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Dashboard Header -->
        <Border Grid.Row="0" Background="#161B22" Padding="32,24">
            <Border.Effect>
                <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="1" Opacity="0.3" BlurRadius="4"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="Haug Components"
                               FontSize="28"
                               FontWeight="SemiBold"
                               Foreground="#F0F6FF"
                               Margin="0,0,0,4"/>
                    <TextBlock Text="Server Infrastructure Dashboard"
                               FontSize="16"
                               FontWeight="Normal"
                               Foreground="#8B949E"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Border Background="#21262D"
                            CornerRadius="6"
                            Padding="16,12"
                            Margin="0,0,12,0"
                            BorderBrush="#30363D"
                            BorderThickness="1">
                        <StackPanel>
                            <TextBlock Text="Last Update"
                                       FontSize="11"
                                       FontWeight="Medium"
                                       Foreground="#7D8590"
                                       Margin="0,0,0,4"/>
                            <TextBlock x:Name="LastUpdateTextBlock"
                                       Text="Just now"
                                       FontSize="14"
                                       FontWeight="SemiBold"
                                       Foreground="#F0F6FF"/>
                        </StackPanel>
                    </Border>

                    <Border Background="#21262D"
                            CornerRadius="6"
                            Padding="16,12"
                            BorderBrush="#30363D"
                            BorderThickness="1">
                        <StackPanel>
                            <TextBlock Text="Current Time"
                                       FontSize="11"
                                       FontWeight="Medium"
                                       Foreground="#7D8590"
                                       Margin="0,0,0,4"/>
                            <TextBlock x:Name="TimeTextBlock"
                                       Text="11:30:00"
                                       FontSize="14"
                                       FontWeight="SemiBold"
                                       Foreground="#F0F6FF"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Dashboard Statistics -->
        <Border Grid.Row="1" Background="#0D1117" Padding="32,24" BorderBrush="#21262D" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Total Servers -->
                <Border Grid.Column="0" Background="#161B22" CornerRadius="8" Padding="24,20" Margin="0,0,16,0" BorderBrush="#30363D" BorderThickness="1">
                    <Border.Effect>
                        <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                    </Border.Effect>
                    <StackPanel>
                        <Border Background="#21262D" CornerRadius="8" Width="48" Height="48" Margin="0,0,0,16">
                            <TextBlock Text="🖥️" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="TotalServersText" Text="7" FontSize="32" FontWeight="Bold" Foreground="#F0F6FF" Margin="0,0,0,8"/>
                        <TextBlock Text="Total Servers" FontSize="13" FontWeight="Medium" Foreground="#8B949E"/>
                    </StackPanel>
                </Border>

                <!-- Online Servers -->
                <Border Grid.Column="1" Background="#161B22" CornerRadius="8" Padding="24,20" Margin="8,0" BorderBrush="#238636" BorderThickness="1">
                    <Border.Effect>
                        <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                    </Border.Effect>
                    <StackPanel>
                        <Border Background="#1A2F1A" CornerRadius="8" Width="48" Height="48" Margin="0,0,0,16">
                            <TextBlock Text="✓" FontSize="28" FontWeight="Bold" Foreground="#2EA043" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="OnlineServersText" Text="5" FontSize="32" FontWeight="Bold" Foreground="#2EA043" Margin="0,0,0,8"/>
                        <TextBlock Text="Online" FontSize="13" FontWeight="Medium" Foreground="#7C3F00"/>
                    </StackPanel>
                </Border>

                <!-- Offline Servers -->
                <Border Grid.Column="2" Background="#161B22" CornerRadius="8" Padding="24,20" Margin="8,0" BorderBrush="#DA3633" BorderThickness="1">
                    <Border.Effect>
                        <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                    </Border.Effect>
                    <StackPanel>
                        <Border Background="#2F1A1A" CornerRadius="8" Width="48" Height="48" Margin="0,0,0,16">
                            <TextBlock Text="✕" FontSize="24" FontWeight="Bold" Foreground="#F85149" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="OfflineServersText" Text="2" FontSize="32" FontWeight="Bold" Foreground="#F85149" Margin="0,0,0,8"/>
                        <TextBlock Text="Offline" FontSize="13" FontWeight="Medium" Foreground="#8B949E"/>
                    </StackPanel>
                </Border>

                <!-- Average Response -->
                <Border Grid.Column="3" Background="#161B22" CornerRadius="8" Padding="24,20" Margin="8,0,16,0" BorderBrush="#D29922" BorderThickness="1">
                    <Border.Effect>
                        <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                    </Border.Effect>
                    <StackPanel>
                        <Border Background="#2F2A1A" CornerRadius="8" Width="48" Height="48" Margin="0,0,0,16">
                            <TextBlock Text="⚡" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="AvgResponseText" Text="12ms" FontSize="32" FontWeight="Bold" Foreground="#E3B341" Margin="0,0,0,8"/>
                        <TextBlock Text="Avg Response" FontSize="13" FontWeight="Medium" Foreground="#8B949E"/>
                    </StackPanel>
                </Border>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="4" Orientation="Horizontal">
                    <Border Background="#238636" CornerRadius="6" Margin="0,0,12,0">
                        <Button Content="🔄 Refresh All"
                                Background="Transparent"
                                Foreground="White"
                                Padding="20,12"
                                FontWeight="Medium"
                                FontSize="14"
                                BorderThickness="0"
                                Click="RefreshAllButton_Click"/>
                    </Border>
                    <Border Background="#1F6FEB" CornerRadius="6">
                        <Button Content="➕ Add Server"
                                Background="Transparent"
                                Foreground="White"
                                Padding="20,12"
                                FontWeight="Medium"
                                FontSize="14"
                                BorderThickness="0"
                                Click="AddServerButton_Click"/>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Server Grid -->
        <ScrollViewer Grid.Row="2" Padding="32,24" Background="#0D1117">
            <StackPanel>
                <TextBlock Text="Server Overview"
                           FontSize="24"
                           FontWeight="SemiBold"
                           Foreground="#F0F6FF"
                           Margin="0,0,0,24"/>

                <!-- Server Akkordeon -->
                <ItemsControl x:Name="ServerList">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="#161B22"
                                    CornerRadius="8"
                                    Margin="0,0,0,16"
                                    BorderBrush="#30363D"
                                    BorderThickness="1"
                                    UseLayoutRounding="True"
                                    SnapsToDevicePixels="True">
                                <Border.Effect>
                                    <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="3" Opacity="0.25" BlurRadius="12"/>
                                </Border.Effect>

                                <Expander Background="Transparent"
                                          BorderThickness="0"
                                          IsExpanded="False"
                                          Margin="4">
                                    <Expander.Style>
                                        <Style TargetType="Expander">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Expander">
                                                        <Grid>
                                                            <Grid.RowDefinitions>
                                                                <RowDefinition Height="Auto"/>
                                                                <RowDefinition Height="*"/>
                                                            </Grid.RowDefinitions>

                                                            <ToggleButton Grid.Row="0"
                                                                          IsChecked="{Binding IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                                                                          Content="{TemplateBinding Header}"
                                                                          Background="Transparent"
                                                                          BorderThickness="0"
                                                                          HorizontalContentAlignment="Stretch"
                                                                          Padding="16,16">
                                                                <ToggleButton.Style>
                                                                    <Style TargetType="ToggleButton">
                                                                        <Setter Property="Template">
                                                                            <Setter.Value>
                                                                                <ControlTemplate TargetType="ToggleButton">
                                                                                    <Border Background="{TemplateBinding Background}"
                                                                                            CornerRadius="8"
                                                                                            Padding="{TemplateBinding Padding}">
                                                                                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"/>
                                                                                    </Border>
                                                                                    <ControlTemplate.Triggers>
                                                                                        <Trigger Property="IsMouseOver" Value="True">
                                                                                            <Setter Property="Background" Value="#F5F5F5"/>
                                                                                        </Trigger>
                                                                                    </ControlTemplate.Triggers>
                                                                                </ControlTemplate>
                                                                            </Setter.Value>
                                                                        </Setter>
                                                                    </Style>
                                                                </ToggleButton.Style>
                                                            </ToggleButton>

                                                            <ContentPresenter Grid.Row="1"
                                                                              x:Name="ExpanderContent"
                                                                              Visibility="Collapsed"/>
                                                        </Grid>

                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsExpanded" Value="True">
                                                                <Setter TargetName="ExpanderContent" Property="Visibility" Value="Visible"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Expander.Style>

                                <!-- Akkordeon Header -->
                                <Expander.Header>
                                    <Border Background="Transparent"
                                            UseLayoutRounding="True">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="200"/>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="100"/>
                                                <ColumnDefinition Width="120"/>
                                                <ColumnDefinition Width="100"/>
                                                <ColumnDefinition Width="100"/>
                                                <ColumnDefinition Width="20"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Server Icon -->
                                            <Border Grid.Column="0"
                                                    Background="#1F6FEB"
                                                    CornerRadius="6"
                                                    Width="48"
                                                    Height="48"
                                                    Margin="0,0,16,0">
                                                <TextBlock Text="💻"
                                                           FontSize="24"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                            </Border>

                                            <!-- Server Info -->
                                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding Name}"
                                                           FontWeight="SemiBold"
                                                           FontSize="16"
                                                           Foreground="#F0F6FF"
                                                           Margin="0,0,0,4"/>
                                                <TextBlock Text="{Binding Description}"
                                                           FontSize="12"
                                                           Foreground="#8B949E"/>
                                            </StackPanel>

                                            <!-- IP Address -->
                                            <Border Grid.Column="2"
                                                    Background="#21262D"
                                                    CornerRadius="6"
                                                    Padding="12,8"
                                                    VerticalAlignment="Center"
                                                    BorderBrush="#30363D"
                                                    BorderThickness="1">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="🌐" FontSize="14" Margin="0,0,6,0"/>
                                                    <TextBlock Text="{Binding IpAddress}"
                                                               FontSize="12"
                                                               FontWeight="Medium"
                                                               Foreground="#F0F6FF"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- CPU -->
                                            <Border Grid.Column="3"
                                                    Background="#2F2A1A"
                                                    CornerRadius="6"
                                                    Padding="12,8"
                                                    VerticalAlignment="Center"
                                                    BorderBrush="#D29922"
                                                    BorderThickness="1">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="⚡" FontSize="14" Margin="0,0,6,0"/>
                                                    <TextBlock Text="{Binding CpuUsage, StringFormat={}{0}%}"
                                                               FontSize="12"
                                                               FontWeight="Medium"
                                                               Foreground="#E3B341"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- Alerts/Warnings -->
                                            <Border Grid.Column="4"
                                                    Background="{Binding AlertBackgroundColor}"
                                                    CornerRadius="4"
                                                    Padding="12,8"
                                                    VerticalAlignment="Center">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding AlertIcon}" FontSize="14" Margin="0,0,6,0"/>
                                                    <TextBlock Text="{Binding AlertText}"
                                                               FontSize="11"
                                                               FontWeight="Medium"
                                                               Foreground="{Binding AlertTextColor}"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- Uptime -->
                                            <Border Grid.Column="5"
                                                    Background="#E8F5E8"
                                                    CornerRadius="4"
                                                    Padding="12,8"
                                                    VerticalAlignment="Center">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="⏱️" FontSize="14" Margin="0,0,6,0"/>
                                                    <TextBlock Text="{Binding UptimeText}"
                                                               FontSize="11"
                                                               FontWeight="Medium"
                                                               Foreground="#2E7D32"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- OS Info -->
                                            <Border Grid.Column="6"
                                                    Background="#E3F2FD"
                                                    CornerRadius="4"
                                                    Padding="12,8"
                                                    VerticalAlignment="Center">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding OsIcon}" FontSize="14" Margin="0,0,6,0"/>
                                                    <TextBlock Text="{Binding OsVersion}"
                                                               FontSize="11"
                                                               FontWeight="Medium"
                                                               Foreground="#1976D2"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- Action Buttons -->
                                            <StackPanel Grid.Column="8"
                                                        Orientation="Horizontal"
                                                        VerticalAlignment="Center"
                                                        HorizontalAlignment="Center">

                                                <!-- Status Update Button -->
                                                <Button Background="#1F6FEB"
                                                        BorderThickness="0"
                                                        Padding="12,10"
                                                        Margin="0,0,8,0"
                                                        ToolTip="Status aktualisieren"
                                                        Click="RefreshServerButton_Click"
                                                        Tag="{Binding}"
                                                        Width="36"
                                                        Height="36">
                                                    <Button.Style>
                                                        <Style TargetType="Button">
                                                            <Setter Property="Template">
                                                                <Setter.Value>
                                                                    <ControlTemplate TargetType="Button">
                                                                        <Border Background="{TemplateBinding Background}"
                                                                                CornerRadius="4"
                                                                                Padding="{TemplateBinding Padding}">
                                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                        </Border>
                                                                        <ControlTemplate.Triggers>
                                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                                <Setter Property="Background" Value="#1976D2"/>
                                                                            </Trigger>
                                                                        </ControlTemplate.Triggers>
                                                                    </ControlTemplate>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </Style>
                                                    </Button.Style>
                                                    <TextBlock Text="↻"
                                                               FontSize="16"
                                                               Foreground="White"
                                                               FontWeight="Bold"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                                </Button>

                                                <!-- Restart Button -->
                                                <Button Background="#D29922"
                                                        BorderThickness="0"
                                                        Padding="12,10"
                                                        Margin="0,0,8,0"
                                                        ToolTip="Server neustarten"
                                                        Click="RestartServerButton_Click"
                                                        Tag="{Binding}"
                                                        Width="36"
                                                        Height="36">
                                                    <Button.Style>
                                                        <Style TargetType="Button">
                                                            <Setter Property="Template">
                                                                <Setter.Value>
                                                                    <ControlTemplate TargetType="Button">
                                                                        <Border Background="{TemplateBinding Background}"
                                                                                CornerRadius="4"
                                                                                Padding="{TemplateBinding Padding}">
                                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                        </Border>
                                                                        <ControlTemplate.Triggers>
                                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                                <Setter Property="Background" Value="#F57C00"/>
                                                                            </Trigger>
                                                                        </ControlTemplate.Triggers>
                                                                    </ControlTemplate>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </Style>
                                                    </Button.Style>
                                                    <TextBlock Text="⟲"
                                                               FontSize="16"
                                                               Foreground="White"
                                                               FontWeight="Bold"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                                </Button>

                                                <!-- Delete Button -->
                                                <Button Background="#DA3633"
                                                        BorderThickness="0"
                                                        Padding="12,10"
                                                        ToolTip="Server löschen"
                                                        Click="DeleteServerButton_Click"
                                                        Tag="{Binding}"
                                                        Width="36"
                                                        Height="36">
                                                    <Button.Style>
                                                        <Style TargetType="Button">
                                                            <Setter Property="Template">
                                                                <Setter.Value>
                                                                    <ControlTemplate TargetType="Button">
                                                                        <Border Background="{TemplateBinding Background}"
                                                                                CornerRadius="4"
                                                                                Padding="{TemplateBinding Padding}">
                                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                        </Border>
                                                                        <ControlTemplate.Triggers>
                                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                                <Setter Property="Background" Value="#D32F2F"/>
                                                                            </Trigger>
                                                                        </ControlTemplate.Triggers>
                                                                    </ControlTemplate>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </Style>
                                                    </Button.Style>
                                                    <TextBlock Text="✕"
                                                               FontSize="16"
                                                               Foreground="White"
                                                               FontWeight="Bold"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                                </Button>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </Expander.Header>

                                <!-- Akkordeon Content -->
                                <Border Background="#FAFBFC"
                                        CornerRadius="0,0,20,20"
                                        Padding="40,32"
                                        BorderBrush="#E2E8F0"
                                        BorderThickness="0,1,0,0"
                                        Margin="8,0,8,8">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Linke Spalte: System-Ressourcen -->
                                        <StackPanel Grid.Column="0" Margin="0,0,32,0">
                                            <TextBlock Text="📊 System-Ressourcen"
                                                       FontSize="18"
                                                       FontWeight="SemiBold"
                                                       Foreground="#0F172A"
                                                       Margin="0,0,0,24"/>

                                            <!-- CPU Details -->
                                            <Border Background="White"
                                                    CornerRadius="16"
                                                    Padding="24,20"
                                                    Margin="0,0,0,20"
                                                    BorderBrush="#FEF3C7"
                                                    BorderThickness="2">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="1" Opacity="0.05" BlurRadius="8"/>
                                                </Border.Effect>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <Border Grid.Column="0"
                                                            Background="#FEF3C7"
                                                            CornerRadius="10"
                                                            Width="40"
                                                            Height="40"
                                                            Margin="0,0,16,0">
                                                        <TextBlock Text="💻"
                                                                   FontSize="20"
                                                                   HorizontalAlignment="Center"
                                                                   VerticalAlignment="Center"/>
                                                    </Border>

                                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                        <TextBlock Text="CPU-Auslastung"
                                                                   FontSize="14"
                                                                   FontWeight="SemiBold"
                                                                   Foreground="#0F172A"
                                                                   Margin="0,0,0,4"/>
                                                        <TextBlock Text="Prozessor-Aktivität"
                                                                   FontSize="12"
                                                                   Foreground="#64748B"/>
                                                    </StackPanel>

                                                    <TextBlock Grid.Column="2"
                                                               Text="{Binding CpuUsage, StringFormat={}{0}%}"
                                                               FontSize="24"
                                                               FontWeight="Bold"
                                                               Foreground="#D97706"
                                                               VerticalAlignment="Center"/>
                                                </Grid>
                                            </Border>

                                            <!-- Memory Details -->
                                            <Border Background="White"
                                                    CornerRadius="16"
                                                    Padding="24,20"
                                                    Margin="0,0,0,20"
                                                    BorderBrush="#D1FAE5"
                                                    BorderThickness="2">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="1" Opacity="0.05" BlurRadius="8"/>
                                                </Border.Effect>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>

                                                    <Border Grid.Column="0"
                                                            Background="#D1FAE5"
                                                            CornerRadius="10"
                                                            Width="40"
                                                            Height="40"
                                                            Margin="0,0,16,0">
                                                        <TextBlock Text="🧠"
                                                                   FontSize="20"
                                                                   HorizontalAlignment="Center"
                                                                   VerticalAlignment="Center"/>
                                                    </Border>

                                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                        <TextBlock Text="Arbeitsspeicher"
                                                                   FontSize="14"
                                                                   FontWeight="SemiBold"
                                                                   Foreground="#0F172A"
                                                                   Margin="0,0,0,4"/>
                                                        <TextBlock Text="{Binding MemoryUsageText}"
                                                                   FontSize="13"
                                                                   FontWeight="Medium"
                                                                   Foreground="#059669"/>
                                                    </StackPanel>
                                                </Grid>
                                            </Border>
                                        </StackPanel>

                                        <!-- Rechte Spalte: Festplatten-Speicher -->
                                        <StackPanel Grid.Column="1" Margin="32,0,0,0">
                                            <TextBlock Text="💾 Festplatten-Speicher"
                                                       FontSize="18"
                                                       FontWeight="SemiBold"
                                                       Foreground="#0F172A"
                                                       Margin="0,0,0,24"/>

                                            <ItemsControl ItemsSource="{Binding DiskPartitions}">
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate>
                                                        <Border Background="White"
                                                                CornerRadius="16"
                                                                Padding="20,16"
                                                                Margin="0,0,0,16"
                                                                BorderBrush="#EDE9FE"
                                                                BorderThickness="2">
                                                            <Border.Effect>
                                                                <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="1" Opacity="0.05" BlurRadius="8"/>
                                                            </Border.Effect>
                                                            <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="Auto"/>
                                                                    <ColumnDefinition Width="*"/>
                                                                    <ColumnDefinition Width="Auto"/>
                                                                </Grid.ColumnDefinitions>

                                                                <!-- Laufwerksbuchstabe -->
                                                                <Border Grid.Column="0"
                                                                        Background="#7C3AED"
                                                                        CornerRadius="10"
                                                                        Width="36"
                                                                        Height="36"
                                                                        Margin="0,0,16,0">
                                                                    <TextBlock Text="{Binding DriveLetter}"
                                                                               FontSize="16"
                                                                               FontWeight="Bold"
                                                                               Foreground="White"
                                                                               HorizontalAlignment="Center"
                                                                               VerticalAlignment="Center"/>
                                                                </Border>

                                                                <!-- Laufwerk-Info -->
                                                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                                    <TextBlock Text="{Binding Label}"
                                                                               FontSize="14"
                                                                               FontWeight="SemiBold"
                                                                               Foreground="#0F172A"
                                                                               Margin="0,0,0,4"/>
                                                                    <TextBlock Text="{Binding DisplayText}"
                                                                               FontSize="12"
                                                                               FontWeight="Medium"
                                                                               Foreground="#64748B"/>
                                                                </StackPanel>

                                                                <!-- Auslastung -->
                                                                <Border Grid.Column="2"
                                                                        Background="{Binding UsageColor}"
                                                                        CornerRadius="12"
                                                                        Padding="12,6"
                                                                        VerticalAlignment="Center">
                                                                    <TextBlock Text="{Binding UsagePercent, StringFormat={}{0:F0}%}"
                                                                               FontSize="13"
                                                                               FontWeight="Bold"
                                                                               Foreground="White"/>
                                                                </Border>
                                                            </Grid>
                                                        </Border>
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                                </Expander>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>

        <!-- Dashboard Footer -->
        <Border Grid.Row="3"
                Background="#0F172A"
                Padding="32,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Text="© 2025 Haug Components - Server-Infrastruktur Management"
                           FontSize="13"
                           FontWeight="Medium"
                           Foreground="#64748B"
                           VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Border Background="#1E293B"
                            CornerRadius="8"
                            Padding="12,6"
                            Margin="0,0,16,0">
                        <TextBlock Text="⚡ Auto-Update: 30s"
                                   FontSize="12"
                                   FontWeight="Medium"
                                   Foreground="#94A3B8"
                                   VerticalAlignment="Center"/>
                    </Border>
                    <Border Background="#1E293B"
                            CornerRadius="8"
                            Padding="12,6">
                        <TextBlock Text="🟢 System aktiv"
                                   FontSize="12"
                                   Foreground="#10B981"
                                   FontWeight="SemiBold"/>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
