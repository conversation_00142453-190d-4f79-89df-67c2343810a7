<Window
    x:Class="ServerManagementApp.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:ServerManagementApp"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="Server Management App" Height="900" Width="1600"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    UseLayoutRounding="True"
    TextOptions.TextFormattingMode="Display"
    TextOptions.TextRenderingMode="ClearType"
    RenderOptions.BitmapScalingMode="HighQuality">

    <Window.Resources>
        <local:PercentToWidthConverter x:Key="PercentToWidthConverter"/>
        <local:PercentToColorConverter x:Key="PercentToColorConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title Bar -->
        <Border Grid.Row="0" Background="#FF0078D4" Padding="16,8">
            <TextBlock Text="Server Management App"
                       FontSize="20"
                       FontWeight="SemiBold"
                       Foreground="White"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" Padding="20">
            <StackPanel>
                <TextBlock Text="Server Management Dashboard"
                           FontSize="24"
                           FontWeight="Bold"
                           HorizontalAlignment="Center"
                           Margin="0,20,0,10"/>

                <TextBlock Text="Überwachung der Haug Components Server-Infrastruktur"
                           FontSize="16"
                           TextWrapping="Wrap"
                           HorizontalAlignment="Center"
                           Foreground="Gray"
                           Margin="0,0,0,20"/>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            Margin="0,0,0,20">
                    <Button Content="Alle Server pingen"
                            Background="#FF0078D4"
                            Foreground="White"
                            Padding="15,8"
                            Margin="0,0,15,0"
                            Click="RefreshAllButton_Click"/>
                    <Button Content="Server hinzufügen"
                            Padding="15,8"
                            Margin="0,0,15,0"
                            Click="AddServerButton_Click"/>
                    <Button Content="Einstellungen"
                            Padding="15,8"
                            Click="SettingsButton_Click"/>
                </StackPanel>

                <!-- Server List -->
                <ItemsControl x:Name="ServerList" Margin="0,20,0,0">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <UniformGrid Columns="3" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="White"
                                    CornerRadius="8"
                                    Padding="0"
                                    Margin="16"
                                    MinHeight="280"
                                    BorderBrush="#D1D5DB"
                                    BorderThickness="1"
                                    UseLayoutRounding="True"
                                    SnapsToDevicePixels="True">
                                <Border.Effect>
                                    <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="1" Opacity="0.15" BlurRadius="6"/>
                                </Border.Effect>

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header mit Status -->
                                    <Border Grid.Row="0"
                                            Background="#F9FAFB"
                                            CornerRadius="8,8,0,0"
                                            Padding="24,20"
                                            UseLayoutRounding="True">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                                <TextBlock Text="{Binding Name}"
                                                           FontWeight="Bold"
                                                           FontSize="20"
                                                           Foreground="#1F2937"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,16,0"/>
                                                <TextBlock Text="{Binding Description}"
                                                           FontSize="14"
                                                           Foreground="#6B7280"
                                                           VerticalAlignment="Center"/>
                                            </StackPanel>

                                            <Border Grid.Column="1"
                                                    CornerRadius="6"
                                                    Padding="16,8"
                                                    Background="{Binding StatusColor}"
                                                    VerticalAlignment="Center"
                                                    UseLayoutRounding="True">
                                                <TextBlock Text="{Binding StatusText}"
                                                           Foreground="White"
                                                           FontWeight="SemiBold"
                                                           FontSize="12"/>
                                            </Border>
                                        </Grid>
                                    </Border>

                                    <!-- Content Area -->
                                    <StackPanel Grid.Row="1" Margin="24,20">

                                        <!-- Network Info -->
                                        <Grid Margin="0,0,0,20">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <Border Grid.Column="0"
                                                    Background="#DBEAFE"
                                                    CornerRadius="6"
                                                    Padding="10,6"
                                                    Margin="0,0,16,0"
                                                    UseLayoutRounding="True">
                                                <TextBlock Text="🌐" FontSize="16"/>
                                            </Border>

                                            <TextBlock Grid.Column="1"
                                                       Text="{Binding IpAddress}"
                                                       FontSize="16"
                                                       FontWeight="SemiBold"
                                                       Foreground="#374151"
                                                       VerticalAlignment="Center"/>

                                            <TextBlock Grid.Column="2"
                                                       Text="{Binding PingTimeText}"
                                                       FontSize="14"
                                                       Foreground="#6B7280"
                                                       VerticalAlignment="Center"/>
                                        </Grid>

                                        <!-- System Resources -->
                                        <Grid Margin="0,0,0,20">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- CPU -->
                                            <Border Grid.Column="0"
                                                    Background="#FEF3C7"
                                                    CornerRadius="8"
                                                    Padding="16,12"
                                                    Margin="0,0,8,0"
                                                    UseLayoutRounding="True">
                                                <StackPanel>
                                                    <TextBlock Text="CPU"
                                                               FontSize="13"
                                                               Foreground="#D97706"
                                                               FontWeight="SemiBold"
                                                               Margin="0,0,0,6"/>
                                                    <TextBlock Text="{Binding CpuUsage, StringFormat={}{0}%}"
                                                               FontSize="22"
                                                               FontWeight="Bold"
                                                               Foreground="#92400E"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- Memory -->
                                            <Border Grid.Column="1"
                                                    Background="#D1FAE5"
                                                    CornerRadius="8"
                                                    Padding="16,12"
                                                    Margin="8,0,0,0"
                                                    UseLayoutRounding="True">
                                                <StackPanel>
                                                    <TextBlock Text="RAM"
                                                               FontSize="13"
                                                               Foreground="#059669"
                                                               FontWeight="SemiBold"
                                                               Margin="0,0,0,6"/>
                                                    <TextBlock Text="{Binding MemoryUsageText}"
                                                               FontSize="13"
                                                               FontWeight="SemiBold"
                                                               Foreground="#047857"
                                                               TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </Border>
                                        </Grid>

                                        <!-- Disk Info -->
                                        <Border Background="#EDE9FE"
                                                CornerRadius="8"
                                                Padding="16,12"
                                                UseLayoutRounding="True">
                                            <StackPanel>
                                                <TextBlock Text="💾 Festplatten"
                                                           FontSize="13"
                                                           Foreground="#7C3AED"
                                                           FontWeight="SemiBold"
                                                           Margin="0,0,0,8"/>
                                                <TextBlock Text="C: 65% (65/100 GB)"
                                                           FontSize="13"
                                                           Foreground="#5B21B6"
                                                           Margin="0,2"/>
                                                <TextBlock Text="D: 45% (90/200 GB)"
                                                           FontSize="13"
                                                           Foreground="#5B21B6"
                                                           Margin="0,2"/>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>

                                    <!-- Footer -->
                                    <Border Grid.Row="2"
                                            Background="#F9FAFB"
                                            CornerRadius="0,0,8,8"
                                            Padding="24,16"
                                            UseLayoutRounding="True">
                                        <TextBlock Text="{Binding LastPingText}"
                                                   FontSize="12"
                                                   Foreground="#9CA3AF"
                                                   HorizontalAlignment="Right"/>
                                    </Border>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2"
                Background="LightGray"
                Padding="16,8">
            <Grid>
                <TextBlock Text="Bereit"
                           VerticalAlignment="Center"/>
                <TextBlock x:Name="TimeTextBlock"
                           Text="11:30:00"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           Foreground="Gray"/>
            </Grid>
        </Border>
    </Grid>
</Window>
