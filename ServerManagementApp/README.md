# Server Management App

Eine moderne WPF-Anwendung zur Verwaltung von <PERSON>n, entwickelt mit .NET 8.

## Features

- **Übersichtliches Dashboard**: <PERSON><PERSON><PERSON> den Status verschiedener Server auf einen Blick
- **Server-Monitoring**: Anze<PERSON> von CPU- und RAM-Auslastung
- **Echtzeit-Updates**: Live-Zeitanzeige in der Statusleiste
- **Benutzerfreundliche Oberfläche**: Moderne WPF-UI mit ansprechenden Karten-Layouts

## Aktuelle Server-Übersicht

Die Anwendung zeigt derzeit drei Beispiel-Server:

1. **Web Server**
   - Status: Online
   - CPU: 45%
   - RAM: 2.1GB / 8GB

2. **Database Server**
   - Status: Online
   - CPU: 23%
   - RAM: 4.7GB / 16GB

3. **File Server**
   - Status: Wartung
   - CPU: 12%
   - RAM: 1.2GB / 4GB

## Funktionen

- **Server hinzufügen**: Neue Server zur Überwachung hinzufügen (geplant)
- **Alle Server aktualisieren**: Aktualisierung aller Server-Status
- **Einstellungen**: Konfiguration der Anwendung (geplant)

## Technische Details

- **Framework**: .NET 8 mit WPF
- **UI-Framework**: Windows Presentation Foundation (WPF)
- **Zielplattform**: Windows
- **Sprache**: C#

## Installation und Ausführung

### Voraussetzungen
- .NET 8 SDK oder höher
- Windows 10/11

### Projekt ausführen

```bash
# Projekt erstellen
dotnet build ServerManagementApp/ServerManagementApp.csproj

# Anwendung starten
dotnet run --project ServerManagementApp/ServerManagementApp.csproj
```

## Projektstruktur

```
ServerManagementApp/
├── ServerManagementApp.csproj    # Projektdatei
├── App.xaml                      # Anwendungs-XAML
├── App.xaml.cs                   # Anwendungs-Code-Behind
├── MainWindow.xaml               # Hauptfenster-XAML
├── MainWindow.xaml.cs            # Hauptfenster-Code-Behind
└── README.md                     # Diese Datei
```

## Geplante Erweiterungen

- [ ] Echte Server-Verbindungen über SSH/WMI
- [ ] Datenbank-Integration für Server-Konfigurationen
- [ ] Benachrichtigungen bei kritischen Server-Zuständen
- [ ] Historische Daten und Graphen
- [ ] Export-Funktionen für Reports
- [ ] Multi-Server-Gruppen-Management
- [ ] Automatische Server-Discovery

## Entwicklung

Das Projekt wurde als Basis für eine umfassende Server-Management-Lösung entwickelt. Die aktuelle Version zeigt die grundlegende UI-Struktur und kann als Ausgangspunkt für weitere Entwicklungen dienen.

## Lizenz

Dieses Projekt ist für Demonstrationszwecke erstellt worden.
