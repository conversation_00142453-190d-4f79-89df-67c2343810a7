# Server Management App

Eine moderne WPF-Anwendung zur Verwaltung der Haug Components Server-Infrastruktur, entwickelt mit .NET 8.

## Features

- **Echtes Server-Monitoring**: Ping-basierte Überwachung der Server-Verfügbarkeit
- **Automatische Updates**: Alle 30 Sekunden werden die Server automatisch gepingt
- **Dynamische Server-Liste**: Verwaltung aller Server in einer übersichtlichen Karten-Ansicht
- **Persistente Speicherung**: Server-Konfigurationen werden in JSON-Datei gespeichert
- **Echtzeit-Updates**: Live-Zeitanzeige und Server-Status-Updates
- **Server hinzufügen**: Dialog zum Hinzufügen neuer Server zur Überwachung

## Überwachte Server

Die Anwendung überwacht derzeit folgende Server der Haug Components Infrastruktur:

1. **DC01** (172.29.147.10)
   - Domain Controller 1
   - RAM: 8GB

2. **DC02** (172.29.147.11)
   - Domain Controller 2
   - RAM: 8GB

3. **APP01** (*************)
   - Application Server
   - RAM: 16GB

4. **DB01** (************)
   - Database Server
   - RAM: 32GB

5. **MAIL01** (************)
   - Mail Server
   - RAM: 16GB

6. **FILE** (*************)
   - File Server
   - RAM: 12GB

7. **TS01** (*************)
   - Terminal Server
   - RAM: 24GB

## Funktionen

- **Alle Server pingen**: Manuelle Aktualisierung aller Server-Status mit Ping-Test
- **Server hinzufügen**: Dialog zum Hinzufügen neuer Server mit IP-Validierung
- **Automatisches Monitoring**: Kontinuierliche Überwachung alle 30 Sekunden
- **Server-Statistiken**: Anzeige von Online/Offline-Status und Ping-Zeiten

## Technische Details

- **Framework**: .NET 8 mit WPF
- **UI-Framework**: Windows Presentation Foundation (WPF)
- **Zielplattform**: Windows
- **Sprache**: C#

## Installation und Ausführung

### Voraussetzungen
- .NET 8 SDK oder höher
- Windows 10/11

### Projekt ausführen

```bash
# Projekt erstellen
dotnet build ServerManagementApp/ServerManagementApp.csproj

# Anwendung starten
dotnet run --project ServerManagementApp/ServerManagementApp.csproj
```

## Projektstruktur

```
ServerManagementApp/
├── ServerManagementApp.csproj    # Projektdatei
├── App.xaml + App.xaml.cs        # Anwendungslogik
├── MainWindow.xaml + .cs         # Hauptfenster
├── AddServerDialog.xaml + .cs    # Dialog zum Hinzufügen von Servern
├── Models/
│   └── Server.cs                 # Server-Datenmodell
├── Services/
│   └── ServerService.cs          # Server-Management und Ping-Service
├── servers.json                  # Persistente Server-Konfiguration (wird automatisch erstellt)
├── .gitignore                    # Git-Konfiguration
└── README.md                     # Diese Datei
```

## Aktuelle Features (✅ Implementiert)

- [x] **Echtes Ping-Monitoring**: Alle Server werden regelmäßig gepingt
- [x] **Automatische Updates**: 30-Sekunden-Intervall für Server-Status
- [x] **Persistente Speicherung**: Server-Konfigurationen in JSON-Datei
- [x] **Server hinzufügen**: Dialog mit IP-Validierung
- [x] **Echtzeit-Anzeige**: Live-Updates von Status und Ping-Zeiten
- [x] **Responsive UI**: Dynamische Karten-Ansicht für alle Server

## Geplante Erweiterungen

- [ ] WMI-Integration für CPU/RAM-Monitoring (Windows)
- [ ] SNMP-Support für erweiterte Hardware-Überwachung
- [ ] Benachrichtigungen bei Server-Ausfällen
- [ ] Historische Daten und Performance-Graphen
- [ ] Export-Funktionen für Reports
- [ ] Server-Gruppen-Management
- [ ] Automatische Netzwerk-Discovery
- [ ] Remote-Desktop-Integration

## Entwicklung

Das Projekt ist eine vollständig funktionsfähige Server-Management-Lösung für die Haug Components Infrastruktur. Die Anwendung überwacht echte Server und bietet eine solide Basis für weitere Erweiterungen.

## Lizenz

Dieses Projekt ist für Demonstrationszwecke erstellt worden.
