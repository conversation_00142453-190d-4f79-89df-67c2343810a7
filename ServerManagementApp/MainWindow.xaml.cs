using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using ServerManagementApp.Services;
using ServerManagementApp.Models;

namespace ServerManagementApp
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private DispatcherTimer timer;
        private DispatcherTimer pingTimer;
        private ServerService serverService;

        public MainWindow()
        {
            try
            {
                InitializeComponent();

                // Server Service initialisieren
                serverService = new ServerService();
                serverService.InitializeDefaultServers();
                ServerList.ItemsSource = serverService.Servers;

                // Timer für die Zeitanzeige
                timer = new DispatcherTimer();
                timer.Interval = TimeSpan.FromSeconds(1);
                timer.Tick += Timer_Tick;
                timer.Start();

                // Timer für automatische Ping-Updates (alle 30 Sekunden)
                pingTimer = new DispatcherTimer();
                pingTimer.Interval = TimeSpan.FromSeconds(30);
                pingTimer.Tick += PingTimer_Tick;
                pingTimer.Start();

                UpdateTime();

                // Initiales Pingen aller Server
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await serverService.PingAllServersAsync();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Ping error: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Fehler beim Initialisieren der Anwendung: {ex.Message}", "Fehler", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            UpdateTime();
        }

        private async void PingTimer_Tick(object sender, EventArgs e)
        {
            await serverService.PingAllServersAsync();
            UpdateDashboardStatistics();
            LastUpdateTextBlock.Text = DateTime.Now.ToString("HH:mm:ss");
        }

        private void UpdateTime()
        {
            TimeTextBlock.Text = DateTime.Now.ToString("HH:mm:ss");
        }

        private void AddServerButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new AddServerDialog();
            if (dialog.ShowDialog() == true)
            {
                var newServer = new Server
                {
                    Name = dialog.ServerName,
                    IpAddress = dialog.IpAddress,
                    Description = dialog.Description,
                    Status = ServerStatus.Unknown,
                    MemoryTotal = dialog.MemoryTotal * 1024 * 1024 * 1024 // Convert GB to bytes
                };

                serverService.AddServer(newServer);

                // Ping den neuen Server
                _ = Task.Run(async () => await serverService.PingServerAsync(newServer));
            }
        }

        private async void RefreshAllButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Zeige Loading-Indikator (vereinfacht)
                this.Cursor = Cursors.Wait;

                await serverService.PingAllServersAsync();
                UpdateDashboardStatistics();
                LastUpdateTextBlock.Text = DateTime.Now.ToString("HH:mm:ss");

                MessageBox.Show($"✅ Alle {serverService.Servers.Count} Server wurden erfolgreich aktualisiert!\n\nOnline: {serverService.Servers.Count(s => s.Status == ServerStatus.Online)}\nOffline: {serverService.Servers.Count(s => s.Status != ServerStatus.Online)}",
                              "Server-Aktualisierung abgeschlossen",
                              MessageBoxButton.OK,
                              MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Fehler beim Aktualisieren der Server: {ex.Message}",
                              "Fehler",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
            }
            finally
            {
                this.Cursor = Cursors.Arrow;
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            var onlineServers = serverService.Servers.Count(s => s.Status == ServerStatus.Online);
            var totalServers = serverService.Servers.Count;
            var avgResponse = serverService.Servers.Where(s => s.Status == ServerStatus.Online && s.PingTime > 0)
                                                  .Select(s => s.PingTime)
                                                  .DefaultIfEmpty(0)
                                                  .Average();

            MessageBox.Show($"📊 Dashboard-Statistiken\n\n" +
                          $"🖥️ Gesamt Server: {totalServers}\n" +
                          $"✅ Online: {onlineServers}\n" +
                          $"❌ Offline: {totalServers - onlineServers}\n" +
                          $"⚡ Ø Antwortzeit: {(avgResponse > 0 ? $"{avgResponse:F0}ms" : "N/A")}\n\n" +
                          $"🔄 Auto-Update: Alle 30 Sekunden\n" +
                          $"🌐 Netzwerk-Monitoring: Aktiv",
                          "Haug Components - System-Informationen",
                          MessageBoxButton.OK,
                          MessageBoxImage.Information);
        }

        private void UpdateDashboardStatistics()
        {
            var totalServers = serverService.Servers.Count;
            var onlineServers = serverService.Servers.Count(s => s.Status == ServerStatus.Online);
            var offlineServers = totalServers - onlineServers;
            var avgResponse = serverService.Servers.Where(s => s.Status == ServerStatus.Online && s.PingTime > 0)
                                                  .Select(s => s.PingTime)
                                                  .DefaultIfEmpty(0)
                                                  .Average();

            TotalServersText.Text = totalServers.ToString();
            OnlineServersText.Text = onlineServers.ToString();
            OfflineServersText.Text = offlineServers.ToString();
            AvgResponseText.Text = avgResponse > 0 ? $"{avgResponse:F0}ms" : "N/A";
        }

        protected override void OnClosed(EventArgs e)
        {
            timer?.Stop();
            pingTimer?.Stop();
            serverService?.SaveServers();
            base.OnClosed(e);
        }
    }

    public class PercentToWidthConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double percent)
            {
                // Maximale Breite der Progress Bar ist etwa 100 Pixel
                return Math.Max(2, (percent / 100.0) * 100);
            }
            return 0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class PercentToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double percent)
            {
                return percent switch
                {
                    >= 90 => "Red",
                    >= 80 => "Orange",
                    _ => "Green"
                };
            }
            return "Green";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
