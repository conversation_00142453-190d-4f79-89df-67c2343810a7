using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace ServerManagementApp
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private DispatcherTimer timer;

        public MainWindow()
        {
            InitializeComponent();

            // Timer für die Zeitanzeige
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1);
            timer.Tick += Timer_Tick;
            timer.Start();

            UpdateTime();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            UpdateTime();
        }

        private void UpdateTime()
        {
            TimeTextBlock.Text = DateTime.Now.ToString("HH:mm:ss");
        }

        private void AddServerButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Diese Funktion wird in einer zukünftigen Version implementiert.",
                          "Server hinzufügen",
                          MessageBoxButton.OK,
                          MessageBoxImage.Information);
        }

        private void RefreshAllButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Alle Server werden aktualisiert...\n(Simulation - in einer echten App würden hier die Server-Status abgefragt)",
                          "Server aktualisieren",
                          MessageBoxButton.OK,
                          MessageBoxImage.Information);
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Einstellungsseite wird in einer zukünftigen Version hinzugefügt.",
                          "Einstellungen",
                          MessageBoxButton.OK,
                          MessageBoxImage.Information);
        }
    }
}
