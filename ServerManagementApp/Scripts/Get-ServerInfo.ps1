param(
    [Parameter(Mandatory=$true)]
    [string]$ComputerName,
    
    [Parameter(Mandatory=$false)]
    [PSCredential]$Credential
)

try {
    $sessionParams = @{
        ComputerName = $ComputerName
        ErrorAction = 'Stop'
    }
    
    if ($Credential) {
        $sessionParams.Credential = $Credential
    }
    
    # Test connection first
    if (-not (Test-Connection -ComputerName $ComputerName -Count 1 -Quiet)) {
        throw "Server $ComputerName ist nicht erreichbar"
    }
    
    $result = Invoke-Command @sessionParams -ScriptBlock {
        # CPU Usage
        $cpuUsage = Get-WmiObject -Class Win32_Processor | 
            Measure-Object -Property LoadPercentage -Average | 
            Select-Object -ExpandProperty Average
        
        # Memory Information
        $memory = Get-WmiObject -Class Win32_OperatingSystem
        $totalMemoryGB = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
        $freeMemoryGB = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
        $usedMemoryGB = [math]::Round($totalMemoryGB - $freeMemoryGB, 2)
        
        # Operating System Information
        $os = Get-WmiObject -Class Win32_OperatingSystem
        $osName = $os.Caption
        $lastBootTime = $os.ConvertToDateTime($os.LastBootUpTime)
        
        # Disk Information
        $diskInfo = Get-WmiObject -Class Win32_LogicalDisk -Filter "DriveType=3" | ForEach-Object {
            $totalGB = [math]::Round($_.Size / 1GB, 2)
            $freeGB = [math]::Round($_.FreeSpace / 1GB, 2)
            $usedGB = [math]::Round($totalGB - $freeGB, 2)
            $usagePercent = if ($totalGB -gt 0) { [math]::Round(($usedGB / $totalGB) * 100, 1) } else { 0 }
            
            [PSCustomObject]@{
                DriveLetter = $_.DeviceID.Replace(':', '')
                Label = if ([string]::IsNullOrEmpty($_.VolumeName)) { "Local Disk" } else { $_.VolumeName }
                TotalSpaceGB = $totalGB
                UsedSpaceGB = $usedGB
                FreeSpaceGB = $freeGB
                UsagePercent = $usagePercent
            }
        }
        
        # System Information
        $computerSystem = Get-WmiObject -Class Win32_ComputerSystem
        
        # Pending Updates Check
        $pendingUpdates = $false
        try {
            $updateSession = New-Object -ComObject Microsoft.Update.Session
            $updateSearcher = $updateSession.CreateUpdateSearcher()
            $searchResult = $updateSearcher.Search("IsInstalled=0")
            $pendingUpdates = $searchResult.Updates.Count -gt 0
        } catch {
            # Fallback if Windows Update API is not available
            $pendingUpdates = $false
        }
        
        # Pending Reboot Check
        $pendingReboot = $false
        try {
            $pendingReboot = (Get-ChildItem "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update\RebootRequired" -ErrorAction SilentlyContinue) -ne $null -or
                            (Get-Item "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update\PostRebootReporting" -ErrorAction SilentlyContinue) -ne $null -or
                            (Get-ChildItem "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager" -Name "PendingFileRenameOperations" -ErrorAction SilentlyContinue) -ne $null
        } catch {
            $pendingReboot = $false
        }
        
        # Return structured data
        [PSCustomObject]@{
            ComputerName = $env:COMPUTERNAME
            CpuUsage = [int]$cpuUsage
            TotalMemoryGB = $totalMemoryGB
            UsedMemoryGB = $usedMemoryGB
            FreeMemoryGB = $freeMemoryGB
            OperatingSystem = $osName
            LastBootTime = $lastBootTime
            DiskPartitions = $diskInfo
            HasPendingUpdates = $pendingUpdates
            RequiresReboot = $pendingReboot
            Timestamp = Get-Date
        }
    }
    
    # Convert to JSON for easy parsing in C#
    $result | ConvertTo-Json -Depth 3
    
} catch {
    # Return error information as JSON
    $errorInfo = [PSCustomObject]@{
        Error = $true
        ErrorMessage = $_.Exception.Message
        ComputerName = $ComputerName
        Timestamp = Get-Date
    }
    
    $errorInfo | ConvertTo-Json -Depth 2
}
