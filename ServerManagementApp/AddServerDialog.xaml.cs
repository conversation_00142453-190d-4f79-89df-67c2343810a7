using System;
using System.Net;
using System.Windows;

namespace ServerManagementApp
{
    public partial class AddServerDialog : Window
    {
        public string ServerName { get; private set; }
        public string IpAddress { get; private set; }
        public string Description { get; private set; }
        public long MemoryTotal { get; private set; }

        public AddServerDialog()
        {
            InitializeComponent();
            ServerNameTextBox.Focus();
        }

        private void AddButton_Click(object sender, RoutedEventArgs e)
        {
            // Validierung
            if (string.IsNullOrWhiteSpace(ServerNameTextBox.Text))
            {
                MessageBox.Show("Bitte geben Sie einen Server-Namen ein.", "Validierungsfehler", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                ServerNameTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(IpAddressTextBox.Text))
            {
                MessageBox.Show("Bitte geben Sie eine IP-Adresse ein.", "Validierungsfehler", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                IpAddressTextBox.Focus();
                return;
            }

            // IP-Adresse validieren
            if (!IPAddress.TryParse(IpAddressTextBox.Text, out _))
            {
                MessageBox.Show("Bitte geben Sie eine gültige IP-Adresse ein.", "Validierungsfehler", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                IpAddressTextBox.Focus();
                return;
            }

            // Memory validieren
            if (!long.TryParse(MemoryTextBox.Text, out long memory) || memory <= 0)
            {
                MessageBox.Show("Bitte geben Sie eine gültige RAM-Größe in GB ein.", "Validierungsfehler", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                MemoryTextBox.Focus();
                return;
            }

            // Werte setzen
            ServerName = ServerNameTextBox.Text.Trim();
            IpAddress = IpAddressTextBox.Text.Trim();
            Description = string.IsNullOrWhiteSpace(DescriptionTextBox.Text) 
                         ? $"Server {ServerName}" 
                         : DescriptionTextBox.Text.Trim();
            MemoryTotal = memory;

            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
