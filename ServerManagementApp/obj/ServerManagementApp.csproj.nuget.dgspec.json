{"format": 1, "restore": {"E:\\Haug\\Servermanagement\\ServerManagementApp\\ServerManagementApp.csproj": {}}, "projects": {"E:\\Haug\\Servermanagement\\ServerManagementApp\\ServerManagementApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Haug\\Servermanagement\\ServerManagementApp\\ServerManagementApp.csproj", "projectName": "ServerManagementApp", "projectPath": "E:\\Haug\\Servermanagement\\ServerManagementApp\\ServerManagementApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Haug\\Servermanagement\\ServerManagementApp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows10.0.19041.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows10.0.19041": {"targetAlias": "net8.0-windows10.0.19041.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows10.0.19041": {"targetAlias": "net8.0-windows10.0.19041.0", "dependencies": {"Microsoft.WindowsAppSDK": {"target": "Package", "version": "[1.4.231008000, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.19041.57, 10.0.19041.57]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}