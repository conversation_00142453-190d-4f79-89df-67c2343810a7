is_global = true
build_property.TargetFramework = net9.0-windows10.0.19041.0
build_property.TargetPlatformMinVersion = 10.0.17763.0
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 9A19103F-16F7-4668-BE54-9A1E7A4F7556
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = ServerManagementApp
build_property.ProjectDir = E:\Haug\Servermanagement\ServerManagementApp\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.CsWinRTAotOptimizerEnabled = true
build_property.CsWinRTAotExportsEnabled = 
build_property.CsWinRTRcwFactoryFallbackGeneratorForceOptIn = 
build_property.CsWinRTRcwFactoryFallbackGeneratorForceOptOut = 
build_property.CsWinRTCcwLookupTableGeneratorEnabled = true
build_property.CsWinRTMergeReferencedActivationFactories = 
build_property.CsWinRTAotWarningLevel = 
build_property.CsWinRTUseWindowsUIXamlProjections = false
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 
