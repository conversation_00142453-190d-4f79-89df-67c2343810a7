{"ProjectPath": "E:\\Haug\\Servermanagement\\ServerManagementApp\\ServerManagementApp.csproj", "Language": "C#", "LanguageSourceExtension": ".cs", "OutputPath": "obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\", "ReferenceAssemblies": [{"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\Microsoft.CSharp.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\Microsoft.CSharp.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.InteractiveExperiences.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.InteractiveExperiences.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\Microsoft.VisualBasic.Core.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\Microsoft.VisualBasic.Core.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\Microsoft.VisualBasic.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\Microsoft.VisualBasic.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\Microsoft.Win32.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\Microsoft.Win32.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\Microsoft.Win32.Registry.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\Microsoft.Win32.Registry.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.ApplicationModel.Resources.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.ApplicationModel.Resources.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.AppLifecycle.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.AppLifecycle.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.AppNotifications.Builder.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.AppNotifications.Builder.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.AppNotifications.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.AppNotifications.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.PushNotifications.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.PushNotifications.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.net.ref\\10.0.19041.57\\lib\\net8.0\\Microsoft.Windows.SDK.NET.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.net.ref\\10.0.19041.57\\lib\\net8.0\\Microsoft.Windows.SDK.NET.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.Security.AccessControl.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.Security.AccessControl.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.System.Power.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.System.Power.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.System.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.System.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.Widgets.Providers.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.Widgets.Providers.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.WindowsAppRuntime.Bootstrap.Net.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.WindowsAppRuntime.Bootstrap.Net.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.WinUI.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.WinUI.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\mscorlib.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\mscorlib.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\netstandard.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\netstandard.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.AppContext.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.AppContext.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Buffers.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Buffers.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Collections.Concurrent.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Collections.Concurrent.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Collections.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Collections.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Collections.Immutable.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Collections.Immutable.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Collections.NonGeneric.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Collections.NonGeneric.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Collections.Specialized.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Collections.Specialized.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ComponentModel.Annotations.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ComponentModel.Annotations.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ComponentModel.DataAnnotations.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ComponentModel.DataAnnotations.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ComponentModel.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ComponentModel.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ComponentModel.EventBasedAsync.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ComponentModel.EventBasedAsync.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ComponentModel.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ComponentModel.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ComponentModel.TypeConverter.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ComponentModel.TypeConverter.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Configuration.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Configuration.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Console.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Console.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Core.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Core.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Data.Common.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Data.Common.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Data.DataSetExtensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Data.DataSetExtensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Data.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Data.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.Contracts.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.Contracts.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.Debug.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.Debug.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.DiagnosticSource.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.DiagnosticSource.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.FileVersionInfo.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.FileVersionInfo.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.Process.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.Process.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.StackTrace.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.StackTrace.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.TextWriterTraceListener.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.TextWriterTraceListener.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.Tools.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.Tools.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.TraceSource.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.TraceSource.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.Tracing.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Diagnostics.Tracing.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Drawing.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Drawing.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Drawing.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Drawing.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Dynamic.Runtime.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Dynamic.Runtime.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Formats.Asn1.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Formats.Asn1.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Formats.Tar.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Formats.Tar.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Globalization.Calendars.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Globalization.Calendars.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Globalization.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Globalization.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Globalization.Extensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Globalization.Extensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Compression.Brotli.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Compression.Brotli.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Compression.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Compression.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Compression.FileSystem.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Compression.FileSystem.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Compression.ZipFile.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Compression.ZipFile.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.FileSystem.AccessControl.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.FileSystem.AccessControl.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.FileSystem.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.FileSystem.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.FileSystem.DriveInfo.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.FileSystem.DriveInfo.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.FileSystem.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.FileSystem.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.FileSystem.Watcher.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.FileSystem.Watcher.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.IsolatedStorage.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.IsolatedStorage.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.MemoryMappedFiles.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.MemoryMappedFiles.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Pipelines.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Pipelines.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Pipes.AccessControl.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Pipes.AccessControl.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Pipes.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.Pipes.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.UnmanagedMemoryStream.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.IO.UnmanagedMemoryStream.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Linq.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Linq.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Linq.Expressions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Linq.Expressions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Linq.Parallel.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Linq.Parallel.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Linq.Queryable.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Linq.Queryable.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Memory.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Memory.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Http.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Http.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Http.Json.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Http.Json.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.HttpListener.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.HttpListener.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Mail.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Mail.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.NameResolution.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.NameResolution.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.NetworkInformation.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.NetworkInformation.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Ping.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Ping.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Quic.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Quic.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Requests.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Requests.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Security.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Security.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.ServicePoint.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.ServicePoint.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Sockets.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.Sockets.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.WebClient.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.WebClient.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.WebHeaderCollection.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.WebHeaderCollection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.WebProxy.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.WebProxy.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.WebSockets.Client.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.WebSockets.Client.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.WebSockets.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Net.WebSockets.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Numerics.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Numerics.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Numerics.Vectors.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Numerics.Vectors.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ObjectModel.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ObjectModel.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.DispatchProxy.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.DispatchProxy.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.Emit.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.Emit.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.Emit.ILGeneration.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.Emit.ILGeneration.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.Emit.Lightweight.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.Emit.Lightweight.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.Extensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.Extensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.Metadata.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.Metadata.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.TypeExtensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Reflection.TypeExtensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Resources.Reader.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Resources.Reader.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Resources.ResourceManager.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Resources.ResourceManager.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Resources.Writer.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Resources.Writer.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.CompilerServices.Unsafe.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.CompilerServices.Unsafe.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.CompilerServices.VisualC.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.CompilerServices.VisualC.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Extensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Extensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Handles.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Handles.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.InteropServices.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.InteropServices.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.InteropServices.JavaScript.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.InteropServices.JavaScript.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.InteropServices.RuntimeInformation.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.InteropServices.RuntimeInformation.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Intrinsics.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Intrinsics.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Loader.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Loader.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Numerics.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Numerics.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Serialization.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Serialization.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Serialization.Formatters.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Serialization.Formatters.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Serialization.Json.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Serialization.Json.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Serialization.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Serialization.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Serialization.Xml.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Runtime.Serialization.Xml.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.AccessControl.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.AccessControl.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Claims.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Claims.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.Algorithms.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.Algorithms.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.Cng.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.Cng.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.Csp.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.Csp.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.Encoding.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.Encoding.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.OpenSsl.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.OpenSsl.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.X509Certificates.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Cryptography.X509Certificates.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Principal.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Principal.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Principal.Windows.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.Principal.Windows.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.SecureString.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Security.SecureString.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ServiceModel.Web.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ServiceModel.Web.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ServiceProcess.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ServiceProcess.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Text.Encoding.CodePages.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Text.Encoding.CodePages.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Text.Encoding.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Text.Encoding.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Text.Encoding.Extensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Text.Encoding.Extensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Text.Encodings.Web.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Text.Encodings.Web.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Text.Json.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Text.Json.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Text.RegularExpressions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Text.RegularExpressions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Channels.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Channels.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Overlapped.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Overlapped.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Tasks.Dataflow.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Tasks.Dataflow.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Tasks.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Tasks.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Tasks.Extensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Tasks.Extensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Tasks.Parallel.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Tasks.Parallel.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Thread.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Thread.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.ThreadPool.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.ThreadPool.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Timer.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Threading.Timer.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Transactions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Transactions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Transactions.Local.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Transactions.Local.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ValueTuple.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.ValueTuple.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Web.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Web.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Web.HttpUtility.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Web.HttpUtility.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Windows.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Windows.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.Linq.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.Linq.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.ReaderWriter.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.ReaderWriter.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.Serialization.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.Serialization.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.XDocument.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.XDocument.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.XmlDocument.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.XmlDocument.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.XmlSerializer.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.XmlSerializer.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.XPath.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.XPath.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.XPath.XDocument.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\System.Xml.XPath.XDocument.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\WindowsBase.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\9.0.6\\ref\\net9.0\\WindowsBase.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.net.ref\\10.0.19041.57\\lib\\net8.0\\WinRT.Runtime.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.net.ref\\10.0.19041.57\\lib\\net8.0\\WinRT.Runtime.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}], "TargetPlatformMinVersion": "10.0.17763.0", "ReferenceAssemblyPaths": [], "BuildConfiguration": null, "ForceSharedStateShutdown": false, "DisableXbfGeneration": false, "DisableXbfLineInfo": false, "EnableXBindDiagnostics": false, "ClIncludeFiles": null, "CIncludeDirectories": null, "XamlApplications": [{"DependentUpon": "", "FullPath": "E:\\Haug\\Servermanagement\\ServerManagementApp\\App.xaml", "ItemSpec": "App.xaml", "IsSystemReference": false, "IsNuGetReference": false, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}], "XamlPages": [{"DependentUpon": "", "FullPath": "E:\\Haug\\Servermanagement\\ServerManagementApp\\MainWindow.xaml", "ItemSpec": "MainWindow.xaml", "IsSystemReference": false, "IsNuGetReference": false, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}], "LocalAssembly": [{"DependentUpon": "", "FullPath": "E:\\Haug\\Servermanagement\\ServerManagementApp\\obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\intermediatexaml\\ServerManagementApp.dll", "ItemSpec": "obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\intermediatexaml\\ServerManagementApp.dll", "IsSystemReference": false, "IsNuGetReference": false, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}], "SdkXamlPages": null, "ProjectName": "ServerManagementApp", "IsPass1": false, "RootNamespace": "ServerManagementApp", "OutputType": "WinExe", "PriIndexName": null, "CodeGenerationControlFlags": null, "FeatureControlFlags": "EnableXBindDiagnostics;EnableDefaultValidationContextGeneration;EnableWin32Codegen;UsingCSWinRT", "XAMLFingerprint": true, "UseVCMetaManaged": true, "FingerprintIgnorePaths": null, "VCInstallDir": null, "VCInstallPath32": null, "VCInstallPath64": null, "WindowsSdkPath": null, "CompileMode": "RealBuildPass2", "SavedStateFile": "obj\\Debug\\net9.0-windows10.0.19041.0\\win-x64\\\\XamlSaveStateFile.xml", "RootsLog": null, "SuppressWarnings": null, "GenXbfPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.4.231008000\\buildTransitive\\..\\tools\\net5.0\\..\\", "PrecompiledHeaderFile": null, "XamlResourceMapName": null, "XamlComponentResourceLocation": null, "XamlPlatform": null, "TargetFileName": null, "IgnoreSpecifiedTargetPlatformMinVersion": false}